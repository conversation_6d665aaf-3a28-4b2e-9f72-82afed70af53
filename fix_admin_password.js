const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');

// 连接数据库
mongoose.connect('mongodb://localhost:27017/mydb', {
    useNewUrlParser: true,
    useUnifiedTopology: true
});

// 定义用户模型
const userSchema = new mongoose.Schema({
    username: {
        type: String,
        required: true,
        unique: true,
        minlength: 3,
        maxlength: 20,
        match: /^[a-zA-Z0-9_]+$/
    },
    password: {
        type: String,
        required: true,
        minlength: 6
    },
    phone: {
        type: String,
        validate: {
            validator: function(v) {
                return /^1[3-9]\d{9}$/.test(v);
            },
            message: props => `${props.value} 不是有效的手机号码!`
        },
        unique: true,
        sparse: true
    },
    resetPasswordToken: String,
    resetPasswordExpires: Date,
    role: {
        type: String,
        enum: ['admin', 'user'],
        default: 'user'
    }
}, {
    timestamps: true
});

// 密码哈希中间件
userSchema.pre('save', async function(next) {
    if (this.isModified('password')) {
        this.password = await bcrypt.hash(this.password, 10);
    }
    next();
});

// 添加密码验证方法
userSchema.methods.comparePassword = async function(candidatePassword) {
    return bcrypt.compare(candidatePassword, this.password);
};

const User = mongoose.model('User', userSchema);

async function fixAdminPassword() {
    try {
        console.log('=== 检查和修复admin用户密码 ===\n');
        
        // 查找admin用户
        const adminUser = await User.findOne({ username: 'admin' });
        
        if (!adminUser) {
            console.log('未找到admin用户，创建新的admin用户...');
            
            // 创建新的admin用户
            const newAdmin = new User({
                username: 'admin',
                password: 'admin123', // 默认密码
                role: 'admin'
            });
            
            await newAdmin.save();
            console.log('✅ 已创建admin用户，密码: admin123');
            
        } else {
            console.log('找到admin用户:', {
                userId: adminUser._id,
                username: adminUser.username,
                role: adminUser.role,
                createdAt: adminUser.createdAt,
                updatedAt: adminUser.updatedAt
            });
            
            // 测试当前密码
            const testPasswords = ['admin123', 'admin', '123456', 'password'];
            let currentPassword = null;
            
            console.log('\n测试常用密码...');
            for (const testPwd of testPasswords) {
                const isMatch = await adminUser.comparePassword(testPwd);
                console.log(`测试密码 "${testPwd}": ${isMatch ? '✅ 匹配' : '❌ 不匹配'}`);
                if (isMatch) {
                    currentPassword = testPwd;
                    break;
                }
            }
            
            if (currentPassword) {
                console.log(`\n✅ 当前密码是: ${currentPassword}`);
                console.log('密码验证正常，无需修复。');
            } else {
                console.log('\n❌ 无法匹配任何常用密码，重置密码...');
                
                // 重置密码为admin123
                adminUser.password = 'admin123';
                await adminUser.save();
                
                console.log('✅ 已重置admin密码为: admin123');
                
                // 验证重置后的密码
                const verifyReset = await adminUser.comparePassword('admin123');
                console.log(`验证重置后的密码: ${verifyReset ? '✅ 成功' : '❌ 失败'}`);
            }
        }
        
        console.log('\n=== 最终验证 ===');
        const finalUser = await User.findOne({ username: 'admin' });
        if (finalUser) {
            const finalTest = await finalUser.comparePassword('admin123');
            console.log(`最终密码验证 (admin123): ${finalTest ? '✅ 成功' : '❌ 失败'}`);
            
            if (finalTest) {
                console.log('\n🎉 admin用户密码修复完成！');
                console.log('用户名: admin');
                console.log('密码: admin123');
            } else {
                console.log('\n❌ 密码修复失败，请检查数据库连接和用户模型');
            }
        }
        
    } catch (error) {
        console.error('修复失败:', error);
    } finally {
        mongoose.connection.close();
    }
}

fixAdminPassword();
