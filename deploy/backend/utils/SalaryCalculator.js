const { calculateSocialInsurance, calculateIncomeTax, roundToTwo } = require('./calculationUtils');
const salaryConfig = require('../config/salaryConfig');
const { SALARY_CONFIG: CONFIG } = salaryConfig;

// 添加配置验证
if (!CONFIG || !CONFIG.BASE_SALARY) {
    throw new Error('薪资基础配置缺失');
}

/**
 * 获取学历系数 - 与前端保持一致
 * @param {string} education - 学历
 * @returns {number} 学历系数
 */
const getEducationCoefficient = (education) => {
    if (!education) return 1.0;
    
    // 直接使用学历获取系数
    return CONFIG.EDUCATION_COEFFICIENT[education] || 1.0;
};

// 添加薪资计算的缓存机制
const salaryCache = new Map();

const calculateMonthlySalary = ({
    positionLevel,
    positionType,
    education,
    languageLevel,
    administrativeLevel,
    performanceCoefficient = 1.0,
    actualAttendance = 22,
    requiredAttendance = 22,
    specialAllowance = { amount: 0 },
    specialDeduction = { amount: 0 },
    isProbation = false,
    workType = '全职',
    probationEndDate = '',
    educationCoefficient = null,
    languageCoefficient = null,
    educationAdjustment = null,
    languageAdjustment = null
}) => {
    // 生成缓存键
    const cacheKey = JSON.stringify({
        positionLevel,
        positionType,
        education,
        languageLevel,
        administrativeLevel,
        performanceCoefficient,
        actualAttendance,
        requiredAttendance,
        specialAllowance,
        specialDeduction,
        isProbation,
        workType,
        probationEndDate,
        educationCoefficient,
        languageCoefficient,
        educationAdjustment,
        languageAdjustment
    });

    // 检查缓存
    if (salaryCache.has(cacheKey)) {
        return salaryCache.get(cacheKey);
    }

    // 参数验证
    if (!positionLevel || !positionType || !education) {
        throw new Error("必要参数缺失：职级、岗位类型和学历是必填项");
    }

    // 系数计算 - 优先使用前端传递的系数
    let educationCoeff, languageCoeff, finalEducationAdjustment, finalLanguageAdjustment;
    
    if (educationCoefficient !== null && educationCoefficient !== undefined) {
        // 使用前端传递的学历系数
        educationCoeff = Number(educationCoefficient);
        console.log('使用前端传递的学历系数:', educationCoeff);
    } else {
        // 后端计算学历系数
        educationCoeff = getEducationCoefficient(education);
        console.log('后端计算学历系数:', educationCoeff, 'for education:', education);
    }
    
    if (languageCoefficient !== null && languageCoefficient !== undefined) {
        // 使用前端传递的语言系数
        languageCoeff = Number(languageCoefficient);
        console.log('使用前端传递的语言系数:', languageCoeff);
    } else {
        // 后端计算语言系数
        languageCoeff = CONFIG.LANGUAGE_COEFFICIENT[languageLevel] || 1.0;
        console.log('后端计算语言系数:', languageCoeff, 'for languageLevel:', languageLevel);
    }

    // 调整值计算 - 优先使用前端传递的调整值
    const baseSalary = CONFIG.BASE_SALARY;
    
    if (educationAdjustment !== null && educationAdjustment !== undefined) {
        // 使用前端传递的学历调整值
        finalEducationAdjustment = Number(educationAdjustment);
        console.log('使用前端传递的学历调整值:', finalEducationAdjustment);
    } else {
        // 后端计算学历调整值 - 修复精度问题
        const rawAdjustment = baseSalary * (educationCoeff - 1);
        finalEducationAdjustment = Math.round(rawAdjustment);
        console.log('后端计算学历调整值:', finalEducationAdjustment, '(原始值:', rawAdjustment, ')');
    }
    
    if (languageAdjustment !== null && languageAdjustment !== undefined) {
        // 使用前端传递的语言调整值
        finalLanguageAdjustment = Number(languageAdjustment);
        console.log('使用前端传递的语言调整值:', finalLanguageAdjustment);
    } else {
        // 后端计算语言调整值 - 修复精度问题
        const rawAdjustment = baseSalary * (languageCoeff - 1);
        finalLanguageAdjustment = Math.round(rawAdjustment);
        console.log('后端计算语言调整值:', finalLanguageAdjustment, '(原始值:', rawAdjustment, ')');
    }

    const adminPosition = administrativeLevel || '无';

    // 检查是否在试用期内
    let probationStatus = isProbation;

    // 如果没有明确指定isProbation，但工作类型是试用，则认为是在试用期内
    if (!isProbation && workType === '试用') {
        probationStatus = true;
    }

    // 如果有试用期结束日期，检查当前日期是否在试用期内
    if (!probationStatus && probationEndDate) {
        const today = new Date();
        const endDate = new Date(probationEndDate);
        probationStatus = today <= endDate;
    }

    console.log('试用期状态检查:', {
        isProbation,
        workType,
        probationEndDate,
        probationStatus
    });

    console.log('薪资计算使用的系数和调整值:', {
        educationCoeff,
        languageCoeff,
        finalEducationAdjustment,
        finalLanguageAdjustment,
        baseSalary
    });

    // 基本工资计算 - 使用最终确定的调整值
    const adjustedBaseSalary = baseSalary + finalEducationAdjustment + finalLanguageAdjustment;

    console.log('基本工资计算结果:', {
        baseSalary,
        finalEducationAdjustment,
        finalLanguageAdjustment,
        adjustedBaseSalary
    });

    // 岗位工资计算 - 修改为调用positionSalary函数
    const positionSalaryValue = positionSalary(positionType, positionLevel);

    if (!positionSalaryValue) {
        throw new Error(`无效的岗位级别: ${positionLevel}`);
    }

    // 其他薪资组成计算
    const adminSalary = CONFIG.ADMIN_POSITIONS[adminPosition] || 0;
    const performanceBase = adjustedBaseSalary + positionSalaryValue;
    const performanceBonus = roundToTwo(performanceBase * (performanceCoefficient - 1));
    const specialAllowanceAmount = Number(specialAllowance?.amount || 0);

    // 计算出勤调整系数 - 使用动态的额定出勤天数
    const attendanceRatio = actualAttendance / requiredAttendance;

    console.log('出勤计算详情:', {
        actualAttendance,
        requiredAttendance,
        attendanceRatio,
        positionSalaryValue,
        adminSalary,
        performanceBonus
    });

    // 餐补和通讯补贴保持为固定金额（不按出勤天数调整）
    const mealAllowance = CONFIG.MEAL_ALLOWANCE;
    const communicationAllowance = CONFIG.COMMUNICATION_ALLOWANCE;

    // 计算日工资基数（包含所有可变薪资组成，但不包括餐补和通讯补贴）
    const dailySalaryBase = adjustedBaseSalary + positionSalaryValue + adminSalary + performanceBonus;
    const dailySalary = roundToTwo(dailySalaryBase / requiredAttendance);

    // 计算餐补和通讯补贴的日均金额（用于出勤调整计算）
    const dailyMealAllowance = roundToTwo(CONFIG.MEAL_ALLOWANCE / requiredAttendance);
    const dailyCommunicationAllowance = roundToTwo(CONFIG.COMMUNICATION_ALLOWANCE / requiredAttendance);
    const dailyAllowanceTotal = dailyMealAllowance + dailyCommunicationAllowance;

    console.log('薪资计算详情:', {
        dailySalaryBase,
        requiredAttendance,
        actualAttendance,
        dailySalary,
        mealAllowance,
        communicationAllowance,
        dailyMealAllowance,
        dailyCommunicationAllowance,
        dailyAllowanceTotal
    });

    // 计算出勤调整
    // 当出勤天数少于额定天数时，计算缺勤扣除（包括工资和补贴的扣除）
    const absenceDeduction = attendanceRatio < 1 ? roundToTwo((requiredAttendance - actualAttendance) * (dailySalary + dailyAllowanceTotal)) : 0;
    // 当出勤天数多于额定天数时，计算加班补贴（包括工资和补贴的补贴）
    const attendanceAdjustment = attendanceRatio > 1 ? roundToTwo((actualAttendance - requiredAttendance) * (dailySalary + dailyAllowanceTotal)) : 0;

    // 计算试用期工资调整
    // 试用期员工：先计算完整的应发工资，然后整体应用80%
    const probationFactor = probationStatus ? 0.8 : 1.0;

    // 计算正常情况下的完整应发工资（使用固定的餐补和通讯补贴）
    const regularTotalSalary = roundToTwo(
        adjustedBaseSalary + // 基本工资
        positionSalaryValue + // 岗位工资
        adminSalary + // 管理津贴
        performanceBonus + // 绩效奖金
        mealAllowance + // 餐补（固定金额）
        communicationAllowance + // 通讯补贴（固定金额）
        specialAllowanceAmount + // 特殊津贴
        attendanceAdjustment - // 加上出勤调整
        absenceDeduction // 减去缺勤扣除
    );

    // 如果是试用期，对总薪资应用80%
    const totalMonthlySalary = probationStatus ? roundToTwo(regularTotalSalary * probationFactor) : regularTotalSalary;

    // 各项薪资组成部分保持原始值（不单独应用80%）
    // 试用期员工的各个薪资组成部分显示原始值，只有总薪资体现80%折扣

    console.log('试用期薪资调整:', {
        probationStatus,
        probationFactor,
        regularTotalSalary,
        finalTotalSalary: totalMonthlySalary,
        baseSalary: adjustedBaseSalary,
        positionSalary: positionSalaryValue,
        adminSalary,
        performanceBonus,
        mealAllowance: mealAllowance,
        communicationAllowance: communicationAllowance
    });

    // 扣除项计算
    // 使用基本工资（adjustedBaseSalary）作为社保计算的基准
    const socialInsurance = calculateSocialInsurance(adjustedBaseSalary).total;
    const specialDeductionAmount = Number(specialDeduction?.amount || 0);
    const taxableIncome = totalMonthlySalary - socialInsurance - specialDeductionAmount;
    const tax = calculateIncomeTax(taxableIncome).tax;
    const netSalary = roundToTwo(totalMonthlySalary - socialInsurance - tax);

    const result = {
        // 基本工资相关字段 - 显示原始值（不应用试用期系数）
        adjustedBaseSalary: adjustedBaseSalary,  // 基本工资（原始值）
        originalBaseSalary: adjustedBaseSalary,  // 原始基本工资（保持一致）
        educationAdjustment: finalEducationAdjustment,
        languageAdjustment: finalLanguageAdjustment,
        educationCoefficient: educationCoeff,
        languageCoefficient: languageCoeff,

        // 岗位相关薪资 - 显示原始值（不应用试用期系数）
        positionSalary: positionSalaryValue,  // 岗位工资（原始值）
        originalPositionSalary: positionSalaryValue,  // 原始岗位工资
        adminSalary: adminSalary,  // 管理津贴（原始值）
        originalAdminSalary: adminSalary,  // 原始管理津贴
        performanceBonus: performanceBonus,  // 绩效奖金（原始值）
        originalPerformanceBonus: performanceBonus,  // 原始绩效奖金
        
        // 试用期相关字段
        isProbation: probationStatus,
        probationFactor: probationFactor,
        
        // 特殊津贴和扣除
        specialAllowance: {
            remark: specialAllowance?.remark || '',
            amount: specialAllowanceAmount
        },
        specialDeduction: {
            amount: specialDeductionAmount
        },
        
        // 将计算结果放入 calculationResult 对象
        calculationResult: {
            mealAllowance: mealAllowance,  // 餐补（固定金额）
            communicationAllowance: communicationAllowance,  // 通讯补贴（固定金额）
            specialAllowanceAmount: specialAllowanceAmount,  // 特殊津贴（原始值）
            specialDeductionAmount: specialDeductionAmount,
            absenceDeduction: absenceDeduction,  // 添加缺勤扣除
            attendanceAdjustment: attendanceAdjustment,  // 添加出勤调整
            taxableIncome,  // 添加应税收入字段，方便调试
            socialInsurance,
            tax,
            totalMonthlySalary,  // 应发工资（试用期已应用80%）
            originalTotalSalary: probationStatus ? regularTotalSalary : undefined, // 添加原始总薪资（仅试用期显示）
            netSalary,
            isProbation: probationStatus, // 添加试用期状态
            probationFactor: probationFactor // 添加试用期系数
        }
    };

    // 存入缓存
    salaryCache.set(cacheKey, result);
    return result;
};

// 年薪计算函数
const calculateYearlySalary = (params, performanceCoefficient = 1.5) => {
    if (!params) {
        throw new Error("没有提供员工数据");
    }

    const monthlyResult = calculateMonthlySalary(params);
    const yearlySalary = monthlyResult.totalMonthlySalary * 12;

    // 计算年终奖
    const bonusBase = monthlyResult.adjustedBaseSalary + monthlyResult.positionSalary;
    const yearEndBonus = roundToTwo(bonusBase * performanceCoefficient);

    return {
        monthlySalary: monthlyResult,
        yearEndBonus,
        totalYearlySalary: roundToTwo(yearlySalary + yearEndBonus)
    };
};

module.exports = {
    calculateMonthlySalary,
    calculateYearlySalary
};

// 岗位工资计算
const positionSalary = (positionType, positionLevel) => {
    if (positionType === '技术') {
        return CONFIG.TECH_POSITIONS['技术'][positionLevel] || 0;
    } else if (positionType === '高管') {
        return CONFIG.MANAGER_POSITIONS['高管'][positionLevel] || 0;
    } else if (positionType === '支持') {
        return CONFIG.SUPPORT_POSITIONS['支持'][positionLevel] || 0;
    } else {
        return CONFIG.OTHER_POSITIONS['其他'][positionLevel] || 0;
    }
};

// 导出计算岗位薪资的函数
module.exports.calculatePositionSalary = (positionType, positionLevel) => {
    console.log('计算岗位薪资:', { positionType, positionLevel });
    console.log('技术岗位配置:', CONFIG.TECH_POSITIONS['技术']);
    console.log('高管岗位配置:', CONFIG.MANAGER_POSITIONS['高管']);
    console.log('其他岗位配置:', CONFIG.OTHER_POSITIONS['其他']);

    return positionSalary(positionType, positionLevel);
};