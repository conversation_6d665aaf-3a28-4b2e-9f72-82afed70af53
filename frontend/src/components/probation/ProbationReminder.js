import React, { useState, useEffect } from 'react';
import { Badge, Dropdown, List, Button, Empty, Spin, message } from 'antd';
import { BellOutlined, ClockCircleOutlined, WarningOutlined } from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import config from '../../config';
import './ProbationReminder.css';

const ProbationReminder = () => {
    const [reminders, setReminders] = useState({ upcoming: [], expired: [], total: 0 });
    const [loading, setLoading] = useState(false);
    const [visible, setVisible] = useState(false);
    const navigate = useNavigate();

    useEffect(() => {
        fetchReminders();
        // 设置定时器，每30分钟检查一次
        const interval = setInterval(fetchReminders, 30 * 60 * 1000);
        return () => clearInterval(interval);
    }, []);

    const fetchReminders = async () => {
        setLoading(true);
        try {
            const token = localStorage.getItem('token');
            const response = await fetch(`${config.apiBaseUrl}/probation/upcoming`, {
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                throw new Error('获取试用期提醒失败');
            }

            const data = await response.json();
            setReminders(data.data || { upcoming: [], expired: [], total: 0 });
        } catch (error) {
            console.error('获取试用期提醒失败:', error);
        } finally {
            setLoading(false);
        }
    };

    const handleMenuClick = () => {
        setVisible(false);
        navigate('/probation-management');
    };

    const renderReminderItem = (item, type) => {
        const isExpired = type === 'expired';
        const icon = isExpired ? <WarningOutlined style={{ color: '#f5222d' }} /> : <ClockCircleOutlined style={{ color: '#faad14' }} />;
        const statusText = isExpired 
            ? `已过期 ${item.daysOverdue} 天` 
            : `还有 ${item.daysRemaining} 天到期`;

        return (
            <List.Item
                key={item.employeeId}
                className={`reminder-item ${isExpired ? 'expired' : 'upcoming'}`}
            >
                <div className="reminder-content">
                    <div className="employee-info">
                        <span className="employee-name">{item.name}</span>
                        <span className="employee-id">({item.employeeId})</span>
                    </div>
                    <div className="department-info">
                        {item.department} - {item.position}
                    </div>
                    <div className="status-info">
                        {icon}
                        <span className={`status-text ${isExpired ? 'expired' : 'upcoming'}`}>
                            {statusText}
                        </span>
                    </div>
                    <div className="date-info">
                        试用期截止：{item.probationEndDate}
                    </div>
                </div>
            </List.Item>
        );
    };

    const dropdownContent = (
        <div className="probation-reminder-dropdown">
            <div className="reminder-header">
                <h4>试用期提醒</h4>
                <Button type="link" size="small" onClick={fetchReminders} loading={loading}>
                    刷新
                </Button>
            </div>
            
            <div className="reminder-content-wrapper">
                {loading ? (
                    <div className="loading-wrapper">
                        <Spin size="small" />
                        <span>加载中...</span>
                    </div>
                ) : (
                    <>
                        {reminders.expired.length > 0 && (
                            <div className="reminder-section">
                                <div className="section-title expired">
                                    <WarningOutlined />
                                    已过期 ({reminders.expired.length})
                                </div>
                                <List
                                    size="small"
                                    dataSource={reminders.expired}
                                    renderItem={(item) => renderReminderItem(item, 'expired')}
                                />
                            </div>
                        )}
                        
                        {reminders.upcoming.length > 0 && (
                            <div className="reminder-section">
                                <div className="section-title upcoming">
                                    <ClockCircleOutlined />
                                    即将到期 ({reminders.upcoming.length})
                                </div>
                                <List
                                    size="small"
                                    dataSource={reminders.upcoming}
                                    renderItem={(item) => renderReminderItem(item, 'upcoming')}
                                />
                            </div>
                        )}
                        
                        {reminders.total === 0 && (
                            <Empty
                                image={Empty.PRESENTED_IMAGE_SIMPLE}
                                description="暂无试用期提醒"
                                style={{ padding: '20px 0' }}
                            />
                        )}
                    </>
                )}
            </div>
            
            {reminders.total > 0 && (
                <div className="reminder-footer">
                    <Button type="primary" block onClick={handleMenuClick}>
                        查看试用期管理
                    </Button>
                </div>
            )}
        </div>
    );

    return (
        <Dropdown
            overlay={dropdownContent}
            trigger={['click']}
            placement="bottomRight"
            visible={visible}
            onVisibleChange={setVisible}
            overlayClassName="probation-reminder-overlay"
        >
            <div className="probation-reminder-trigger">
                <Badge count={reminders.total} size="small" offset={[0, 0]}>
                    <BellOutlined 
                        style={{ 
                            fontSize: '18px', 
                            color: reminders.total > 0 ? '#1890ff' : '#666',
                            cursor: 'pointer'
                        }} 
                    />
                </Badge>
            </div>
        </Dropdown>
    );
};

export default ProbationReminder;
