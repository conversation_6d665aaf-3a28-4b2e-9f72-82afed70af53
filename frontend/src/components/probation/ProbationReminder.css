/* 试用期提醒触发器 */
.probation-reminder-trigger {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 8px;
    border-radius: 6px;
    transition: all 0.3s ease;
}

.probation-reminder-trigger:hover {
    background-color: rgba(24, 144, 255, 0.1);
}

/* 下拉菜单覆盖层 */
.probation-reminder-overlay .ant-dropdown-menu {
    padding: 0;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    min-width: 350px;
    max-width: 400px;
}

/* 提醒下拉内容 */
.probation-reminder-dropdown {
    background: white;
    border-radius: 8px;
    overflow: hidden;
}

/* 提醒头部 */
.reminder-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 20px;
    background: linear-gradient(135deg, #1890ff, #40a9ff);
    color: white;
}

.reminder-header h4 {
    margin: 0;
    color: white;
    font-size: 16px;
    font-weight: 600;
}

.reminder-header .ant-btn-link {
    color: white;
    padding: 0;
    height: auto;
    font-size: 12px;
}

.reminder-header .ant-btn-link:hover {
    color: rgba(255, 255, 255, 0.8);
}

/* 提醒内容包装器 */
.reminder-content-wrapper {
    max-height: 400px;
    overflow-y: auto;
}

/* 加载状态 */
.loading-wrapper {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    padding: 20px;
    color: #666;
    font-size: 14px;
}

/* 提醒部分 */
.reminder-section {
    border-bottom: 1px solid #f0f0f0;
}

.reminder-section:last-child {
    border-bottom: none;
}

/* 部分标题 */
.section-title {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 20px;
    font-size: 14px;
    font-weight: 600;
    background-color: #fafafa;
    border-bottom: 1px solid #f0f0f0;
}

.section-title.expired {
    color: #f5222d;
    background-color: #fff2f0;
}

.section-title.upcoming {
    color: #faad14;
    background-color: #fffbe6;
}

/* 提醒项目 */
.reminder-item {
    padding: 12px 20px !important;
    border-bottom: 1px solid #f5f5f5;
    transition: all 0.3s ease;
}

.reminder-item:hover {
    background-color: #f9f9f9;
}

.reminder-item:last-child {
    border-bottom: none;
}

.reminder-item.expired {
    border-left: 3px solid #f5222d;
}

.reminder-item.upcoming {
    border-left: 3px solid #faad14;
}

/* 提醒内容 */
.reminder-content {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

/* 员工信息 */
.employee-info {
    display: flex;
    align-items: center;
    gap: 8px;
}

.employee-name {
    font-weight: 600;
    color: #333;
    font-size: 14px;
}

.employee-id {
    color: #666;
    font-size: 12px;
}

/* 部门信息 */
.department-info {
    color: #666;
    font-size: 12px;
}

/* 状态信息 */
.status-info {
    display: flex;
    align-items: center;
    gap: 6px;
    margin-top: 4px;
}

.status-text {
    font-size: 12px;
    font-weight: 500;
}

.status-text.expired {
    color: #f5222d;
}

.status-text.upcoming {
    color: #faad14;
}

/* 日期信息 */
.date-info {
    color: #999;
    font-size: 11px;
    margin-top: 2px;
}

/* 提醒底部 */
.reminder-footer {
    padding: 16px 20px;
    background-color: #fafafa;
    border-top: 1px solid #f0f0f0;
}

.reminder-footer .ant-btn {
    border-radius: 6px;
    font-weight: 500;
    height: 36px;
}

/* 徽章样式 */
.ant-badge-count {
    background: #f5222d;
    border-radius: 10px;
    font-size: 10px;
    font-weight: 600;
    min-width: 16px;
    height: 16px;
    line-height: 16px;
    padding: 0 4px;
    box-shadow: 0 0 0 1px #fff;
}

/* 空状态样式 */
.ant-empty-small {
    margin: 0;
}

.ant-empty-description {
    color: #999;
    font-size: 12px;
}

/* 滚动条样式 */
.reminder-content-wrapper::-webkit-scrollbar {
    width: 4px;
}

.reminder-content-wrapper::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 2px;
}

.reminder-content-wrapper::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 2px;
}

.reminder-content-wrapper::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .probation-reminder-overlay .ant-dropdown-menu {
        min-width: 300px;
        max-width: 320px;
    }
    
    .reminder-header {
        padding: 12px 16px;
    }
    
    .reminder-header h4 {
        font-size: 14px;
    }
    
    .reminder-item {
        padding: 10px 16px !important;
    }
    
    .reminder-footer {
        padding: 12px 16px;
    }
}

/* 动画效果 */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.probation-reminder-dropdown {
    animation: fadeIn 0.3s ease;
}

/* 列表项动画 */
.reminder-item {
    animation: slideIn 0.3s ease;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateX(-10px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* 高亮效果 */
.reminder-item:hover .employee-name {
    color: #1890ff;
}

.reminder-item:hover .status-info svg {
    transform: scale(1.1);
    transition: transform 0.2s ease;
}
