import React, { useState, useEffect } from 'react';
import { Table, Button, Modal, message, Tag, Space, Card, Statistic, Row, Col, DatePicker, Input, Select } from 'antd';
import { ExclamationCircleOutlined, CheckCircleOutlined, ClockCircleOutlined, WarningOutlined } from '@ant-design/icons';
import moment from 'moment';
import config from '../../config';
import './ProbationManagement.css';

const { TextArea } = Input;
const { Option } = Select;
const { confirm } = Modal;

const ProbationManagement = () => {
    const [employees, setEmployees] = useState([]);
    const [statistics, setStatistics] = useState({});
    const [loading, setLoading] = useState(false);
    const [regularizationModalVisible, setRegularizationModalVisible] = useState(false);
    const [extensionModalVisible, setExtensionModalVisible] = useState(false);
    const [selectedEmployee, setSelectedEmployee] = useState(null);
    const [regularizationForm, setRegularizationForm] = useState({
        regularizationDate: moment().format('YYYY-MM-DD'),
        newPosition: '',
        newDepartment: '',
        newSubDepartment: '',
        remarks: ''
    });
    const [extensionForm, setExtensionForm] = useState({
        newEndDate: '',
        extensionReason: '',
        extensionMonths: 1
    });

    useEffect(() => {
        fetchData();
    }, []);

    const fetchData = async () => {
        setLoading(true);
        try {
            await Promise.all([
                fetchProbationEmployees(),
                fetchStatistics()
            ]);
        } catch (error) {
            message.error('获取数据失败');
        } finally {
            setLoading(false);
        }
    };

    const fetchProbationEmployees = async () => {
        try {
            const token = localStorage.getItem('token');
            const response = await fetch(`${config.apiBaseUrl}/probation/employees`, {
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                throw new Error('获取试用期员工失败');
            }

            const data = await response.json();
            setEmployees(data.data || []);
        } catch (error) {
            console.error('获取试用期员工失败:', error);
            message.error('获取试用期员工失败');
        }
    };

    const fetchStatistics = async () => {
        try {
            const token = localStorage.getItem('token');
            const response = await fetch(`${config.apiBaseUrl}/probation/statistics`, {
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                throw new Error('获取统计信息失败');
            }

            const data = await response.json();
            setStatistics(data.data || {});
        } catch (error) {
            console.error('获取统计信息失败:', error);
        }
    };

    const handleRegularization = (employee) => {
        setSelectedEmployee(employee);
        setRegularizationForm({
            regularizationDate: moment().format('YYYY-MM-DD'),
            newPosition: employee.position || '',
            newDepartment: employee.department || '',
            newSubDepartment: employee.subDepartment || '',
            remarks: ''
        });
        setRegularizationModalVisible(true);
    };

    const handleExtension = (employee) => {
        setSelectedEmployee(employee);
        const currentEndDate = moment(employee.probationEndDate);
        const newEndDate = currentEndDate.add(1, 'month').format('YYYY-MM-DD');
        setExtensionForm({
            newEndDate,
            extensionReason: '',
            extensionMonths: 1
        });
        setExtensionModalVisible(true);
    };

    const confirmRegularization = async () => {
        try {
            const token = localStorage.getItem('token');
            const response = await fetch(`${config.apiBaseUrl}/probation/confirm-regularization`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    employeeId: selectedEmployee.employeeId,
                    ...regularizationForm
                })
            });

            if (!response.ok) {
                throw new Error('转正确认失败');
            }

            message.success('员工转正确认成功');
            setRegularizationModalVisible(false);
            fetchData();
        } catch (error) {
            console.error('转正确认失败:', error);
            message.error('转正确认失败');
        }
    };

    const confirmExtension = async () => {
        try {
            const token = localStorage.getItem('token');
            const response = await fetch(`${config.apiBaseUrl}/probation/extend`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    employeeId: selectedEmployee.employeeId,
                    ...extensionForm
                })
            });

            if (!response.ok) {
                throw new Error('试用期延长失败');
            }

            message.success('试用期延长成功');
            setExtensionModalVisible(false);
            fetchData();
        } catch (error) {
            console.error('试用期延长失败:', error);
            message.error('试用期延长失败');
        }
    };

    const getStatusTag = (employee) => {
        const { probationStatus, daysRemaining, daysOverdue } = employee;
        
        if (probationStatus === 'expired') {
            return <Tag color="red" icon={<WarningOutlined />}>已过期 {daysOverdue}天</Tag>;
        } else if (probationStatus === 'upcoming') {
            return <Tag color="orange" icon={<ClockCircleOutlined />}>还有 {daysRemaining}天</Tag>;
        } else {
            return <Tag color="green" icon={<CheckCircleOutlined />}>正常</Tag>;
        }
    };

    const columns = [
        {
            title: '员工编号',
            dataIndex: 'employeeId',
            key: 'employeeId',
            width: 100
        },
        {
            title: '姓名',
            dataIndex: 'name',
            key: 'name',
            width: 100
        },
        {
            title: '部门',
            dataIndex: 'department',
            key: 'department',
            width: 120
        },
        {
            title: '岗位',
            dataIndex: 'position',
            key: 'position',
            width: 120
        },
        {
            title: '入职日期',
            dataIndex: 'entryDate',
            key: 'entryDate',
            width: 110
        },
        {
            title: '试用期',
            dataIndex: 'probationMonths',
            key: 'probationMonths',
            width: 80,
            render: (months) => months === '无' ? '无' : `${months}个月`
        },
        {
            title: '试用期截止日期',
            dataIndex: 'probationEndDate',
            key: 'probationEndDate',
            width: 130
        },
        {
            title: '状态',
            key: 'status',
            width: 130,
            render: (_, employee) => getStatusTag(employee)
        },
        {
            title: '操作',
            key: 'actions',
            width: 200,
            render: (_, employee) => (
                <Space>
                    <Button 
                        type="primary" 
                        size="small"
                        onClick={() => handleRegularization(employee)}
                    >
                        确认转正
                    </Button>
                    <Button 
                        size="small"
                        onClick={() => handleExtension(employee)}
                    >
                        延长试用期
                    </Button>
                </Space>
            )
        }
    ];

    return (
        <div className="probation-management">
            <div className="probation-header">
                <h2>试用期管理</h2>
                <Button type="primary" onClick={fetchData} loading={loading}>
                    刷新数据
                </Button>
            </div>

            {/* 统计卡片 */}
            <Row gutter={16} style={{ marginBottom: 24 }}>
                <Col span={6}>
                    <Card>
                        <Statistic
                            title="试用期员工总数"
                            value={statistics.total || 0}
                            prefix={<ExclamationCircleOutlined />}
                        />
                    </Card>
                </Col>
                <Col span={6}>
                    <Card>
                        <Statistic
                            title="即将到期"
                            value={statistics.upcoming || 0}
                            valueStyle={{ color: '#faad14' }}
                            prefix={<ClockCircleOutlined />}
                        />
                    </Card>
                </Col>
                <Col span={6}>
                    <Card>
                        <Statistic
                            title="已过期"
                            value={statistics.expired || 0}
                            valueStyle={{ color: '#f5222d' }}
                            prefix={<WarningOutlined />}
                        />
                    </Card>
                </Col>
                <Col span={6}>
                    <Card>
                        <Statistic
                            title="正常"
                            value={statistics.normal || 0}
                            valueStyle={{ color: '#52c41a' }}
                            prefix={<CheckCircleOutlined />}
                        />
                    </Card>
                </Col>
            </Row>

            {/* 员工列表 */}
            <Card title="试用期员工列表">
                <Table
                    columns={columns}
                    dataSource={employees}
                    rowKey="employeeId"
                    loading={loading}
                    pagination={{
                        pageSize: 10,
                        showSizeChanger: true,
                        showQuickJumper: true,
                        showTotal: (total) => `共 ${total} 条记录`
                    }}
                    scroll={{ x: 1000 }}
                />
            </Card>

            {/* 转正确认弹窗 */}
            <Modal
                title="确认员工转正"
                open={regularizationModalVisible}
                onOk={confirmRegularization}
                onCancel={() => setRegularizationModalVisible(false)}
                width={600}
            >
                {selectedEmployee && (
                    <div>
                        <p><strong>员工信息：</strong>{selectedEmployee.name} ({selectedEmployee.employeeId})</p>
                        <p><strong>当前部门：</strong>{selectedEmployee.department}</p>
                        <p><strong>当前岗位：</strong>{selectedEmployee.position}</p>
                        
                        <div style={{ marginTop: 20 }}>
                            <div style={{ marginBottom: 16 }}>
                                <label>转正日期：</label>
                                <DatePicker
                                    value={moment(regularizationForm.regularizationDate)}
                                    onChange={(date) => setRegularizationForm({
                                        ...regularizationForm,
                                        regularizationDate: date.format('YYYY-MM-DD')
                                    })}
                                    style={{ width: '100%' }}
                                />
                            </div>
                            
                            <div style={{ marginBottom: 16 }}>
                                <label>转正后岗位：</label>
                                <Input
                                    value={regularizationForm.newPosition}
                                    onChange={(e) => setRegularizationForm({
                                        ...regularizationForm,
                                        newPosition: e.target.value
                                    })}
                                    placeholder="如不变更请保持原值"
                                />
                            </div>
                            
                            <div style={{ marginBottom: 16 }}>
                                <label>备注：</label>
                                <TextArea
                                    value={regularizationForm.remarks}
                                    onChange={(e) => setRegularizationForm({
                                        ...regularizationForm,
                                        remarks: e.target.value
                                    })}
                                    placeholder="转正备注信息"
                                    rows={3}
                                />
                            </div>
                        </div>
                    </div>
                )}
            </Modal>

            {/* 延长试用期弹窗 */}
            <Modal
                title="延长试用期"
                open={extensionModalVisible}
                onOk={confirmExtension}
                onCancel={() => setExtensionModalVisible(false)}
                width={500}
            >
                {selectedEmployee && (
                    <div>
                        <p><strong>员工信息：</strong>{selectedEmployee.name} ({selectedEmployee.employeeId})</p>
                        <p><strong>当前试用期截止日期：</strong>{selectedEmployee.probationEndDate}</p>
                        
                        <div style={{ marginTop: 20 }}>
                            <div style={{ marginBottom: 16 }}>
                                <label>新的截止日期：</label>
                                <DatePicker
                                    value={moment(extensionForm.newEndDate)}
                                    onChange={(date) => setExtensionForm({
                                        ...extensionForm,
                                        newEndDate: date.format('YYYY-MM-DD')
                                    })}
                                    style={{ width: '100%' }}
                                />
                            </div>
                            
                            <div style={{ marginBottom: 16 }}>
                                <label>延长原因：</label>
                                <TextArea
                                    value={extensionForm.extensionReason}
                                    onChange={(e) => setExtensionForm({
                                        ...extensionForm,
                                        extensionReason: e.target.value
                                    })}
                                    placeholder="请输入延长试用期的原因"
                                    rows={3}
                                />
                            </div>
                        </div>
                    </div>
                )}
            </Modal>
        </div>
    );
};

export default ProbationManagement;
