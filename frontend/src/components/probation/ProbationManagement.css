.probation-management {
    padding: 24px;
    background-color: #f5f5f5;
    min-height: 100vh;
}

.probation-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    padding: 16px 24px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.probation-header h2 {
    margin: 0;
    color: #1890ff;
    font-size: 24px;
    font-weight: 600;
}

/* 统计卡片样式 */
.ant-card {
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.ant-card:hover {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
}

.ant-statistic-title {
    font-size: 14px;
    color: #666;
    margin-bottom: 8px;
}

.ant-statistic-content {
    font-size: 24px;
    font-weight: 600;
}

/* 表格样式 */
.ant-table {
    background: white;
    border-radius: 8px;
}

.ant-table-thead > tr > th {
    background-color: #fafafa;
    border-bottom: 2px solid #e8e8e8;
    font-weight: 600;
    color: #333;
}

.ant-table-tbody > tr:hover > td {
    background-color: #f5f5f5;
}

/* 状态标签样式 */
.ant-tag {
    border-radius: 16px;
    padding: 4px 12px;
    font-size: 12px;
    font-weight: 500;
    display: inline-flex;
    align-items: center;
    gap: 4px;
}

/* 按钮样式 */
.ant-btn {
    border-radius: 6px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.ant-btn-primary {
    background: linear-gradient(135deg, #1890ff, #40a9ff);
    border: none;
    box-shadow: 0 2px 8px rgba(24, 144, 255, 0.3);
}

.ant-btn-primary:hover {
    background: linear-gradient(135deg, #40a9ff, #1890ff);
    box-shadow: 0 4px 12px rgba(24, 144, 255, 0.4);
    transform: translateY(-1px);
}

.ant-btn-default {
    border-color: #d9d9d9;
    color: #666;
}

.ant-btn-default:hover {
    border-color: #1890ff;
    color: #1890ff;
}

/* 弹窗样式 */
.ant-modal-header {
    border-bottom: 1px solid #e8e8e8;
    padding: 16px 24px;
}

.ant-modal-title {
    font-size: 18px;
    font-weight: 600;
    color: #333;
}

.ant-modal-body {
    padding: 24px;
}

.ant-modal-footer {
    border-top: 1px solid #e8e8e8;
    padding: 16px 24px;
}

/* 表单样式 */
.ant-input,
.ant-select-selector,
.ant-picker {
    border-radius: 6px;
    border: 1px solid #d9d9d9;
    transition: all 0.3s ease;
}

.ant-input:focus,
.ant-select-focused .ant-select-selector,
.ant-picker-focused {
    border-color: #1890ff;
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

/* 表单标签样式 */
label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #333;
}

/* 响应式设计 */
@media (max-width: 1200px) {
    .probation-management {
        padding: 16px;
    }
    
    .probation-header {
        flex-direction: column;
        gap: 16px;
        text-align: center;
    }
    
    .ant-table {
        font-size: 12px;
    }
}

@media (max-width: 768px) {
    .probation-management {
        padding: 12px;
    }
    
    .probation-header h2 {
        font-size: 20px;
    }
    
    .ant-statistic-content {
        font-size: 20px;
    }
    
    .ant-btn {
        font-size: 12px;
        padding: 4px 8px;
    }
}

/* 加载状态样式 */
.ant-spin-container {
    transition: opacity 0.3s ease;
}

.ant-spin-blur {
    opacity: 0.5;
    pointer-events: none;
}

/* 空状态样式 */
.ant-empty {
    padding: 40px 0;
}

.ant-empty-description {
    color: #999;
    font-size: 14px;
}

/* 分页样式 */
.ant-pagination {
    margin-top: 16px;
    text-align: right;
}

.ant-pagination-item {
    border-radius: 6px;
}

.ant-pagination-item-active {
    background: #1890ff;
    border-color: #1890ff;
}

.ant-pagination-item-active a {
    color: white;
}

/* 工具提示样式 */
.ant-tooltip-inner {
    background: rgba(0, 0, 0, 0.85);
    border-radius: 6px;
    font-size: 12px;
}

/* 消息提示样式 */
.ant-message {
    top: 24px;
}

.ant-message-notice {
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* 确认对话框样式 */
.ant-modal-confirm .ant-modal-body {
    padding: 32px 32px 24px;
}

.ant-modal-confirm-title {
    font-size: 16px;
    font-weight: 600;
    color: #333;
}

.ant-modal-confirm-content {
    margin-top: 16px;
    color: #666;
    font-size: 14px;
    line-height: 1.5;
}
