import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { message, Table, Input, Select, Modal, Button } from 'antd';
import { FileSearchOutlined, EditOutlined, DeleteOutlined, BellOutlined } from '@ant-design/icons';
import './EmployeeList.css';
import '../common/TextLinkStyles.css';
import ExportDialog from '../../common/ExportDialog';
import ExportUtils from '../../common/ExportUtils';
import config from '../../config';
import { checkNameMatch } from '../../utils/pinyinSearch';
import PrintUtils from '../../common/PrintUtils';
import EmployeeDetails from './EmployeeDetails';
import ProbationReminder from './ProbationReminder';



function EmployeeList() {
    // 员工数据列表，将按工号自动排序（工号首字母和数字升序排列）
    const [employees, setEmployees] = useState([]);
    const navigate = useNavigate();
    const [selectedEmployee, setSelectedEmployee] = useState(null);
    const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
    const [isExportDialogOpen, setIsExportDialogOpen] = useState(false);
    const [isDetailModalVisible, setIsDetailModalVisible] = useState(false);
    const [detailEmployee, setDetailEmployee] = useState(null);
    const [pageSize, setPageSize] = useState(20); // 默认每页显示20条
    const [probationReminderVisible, setProbationReminderVisible] = useState(false);
    const [probationCount, setProbationCount] = useState(0);

    // 定义获取员工数据的函数
    const fetchEmployees = async () => {
        try {
            // 强制使用最新的配置
            config.refresh();
            const apiUrl = `${config.apiBaseUrl}/employees`;
            console.log('获取员工数据，使用API地址:', apiUrl);

            // 添加时间戳参数，确保不使用缓存
            const timestamp = new Date().getTime();
            const response = await fetch(`${apiUrl}?t=${timestamp}`, {
                headers: {
                    'Authorization': `Bearer ${localStorage.getItem('token') || ''}`,
                    'Content-Type': 'application/json',
                    'Cache-Control': 'no-cache, no-store, must-revalidate',
                    'Pragma': 'no-cache',
                    'Expires': '0'
                }
            });

            if (!response.ok) {
                throw new Error('获取数据失败');
            }

            const data = await response.json();
            console.log(`成功获取 ${data.length} 名员工数据`);

            // 检查是否有重复的工号
            const employeeIdMap = {};
            const duplicates = [];

            data.forEach(emp => {
                if (emp.employeeId) {
                    if (employeeIdMap[emp.employeeId]) {
                        duplicates.push(emp.employeeId);
                    } else {
                        employeeIdMap[emp.employeeId] = true;
                    }
                }
            });

            if (duplicates.length > 0) {
                console.warn(`发现 ${duplicates.length} 个重复的工号:`, duplicates);

                // 对于每个重复的工号，只保留一个记录
                const filteredData = [];
                const processedIds = {};

                data.forEach(emp => {
                    if (!emp.employeeId || !processedIds[emp.employeeId]) {
                        filteredData.push(emp);
                        if (emp.employeeId) {
                            processedIds[emp.employeeId] = true;
                        }
                    } else {
                        console.log(`跳过重复的员工记录: ID=${emp._id}, 工号=${emp.employeeId}, 姓名=${emp.name}`);
                    }
                });

                console.log(`过滤后剩余 ${filteredData.length} 名员工数据`);
                setEmployees(filteredData);
            } else {
                setEmployees(data);
            }
        } catch (error) {
            console.error('获取员工列表失败:', error);
            message.error('获取员工列表失败');
        }
    };

    // 检查试用期提醒
    const checkProbationReminders = async () => {
        try {
            const response = await fetch(`${config.apiBaseUrl}/employees/probation-reminders`, {
                headers: {
                    'Authorization': `Bearer ${localStorage.getItem('token')}`,
                    'Content-Type': 'application/json'
                }
            });

            if (response.ok) {
                const data = await response.json();
                setProbationCount(data.length);

                // 如果有即将到期的试用期员工，自动显示提醒弹窗
                if (data.length > 0) {
                    // 检查是否已经在当前会话中显示过提醒
                    const reminderShown = sessionStorage.getItem('probationReminderShown');
                    if (!reminderShown) {
                        setProbationReminderVisible(true);
                        sessionStorage.setItem('probationReminderShown', 'true');
                    }
                }
            }
        } catch (error) {
            console.error('检查试用期提醒失败:', error);
        }
    };

    // 初始加载数据
    useEffect(() => {
        fetchEmployees();
        checkProbationReminders();

        // 添加事件监听器，响应数据库恢复事件
        const handleDatabaseRestored = () => {
            console.log('收到数据库恢复事件，重新获取员工数据...');
            fetchEmployees();
        };

        // 添加事件监听器，响应刷新员工数据事件
        const handleRefreshEmployees = () => {
            console.log('收到刷新员工数据事件，重新获取员工数据...');
            fetchEmployees();
        };

        // 注册事件监听器
        window.addEventListener('database-restored', handleDatabaseRestored);
        window.addEventListener('refresh-employees', handleRefreshEmployees);

        // 清理函数
        return () => {
            window.removeEventListener('database-restored', handleDatabaseRestored);
            window.removeEventListener('refresh-employees', handleRefreshEmployees);
        };
    }, []);

    const [filterDepartment, setFilterDepartment] = useState('all');
    const [searchText, setSearchText] = useState('');

    const handleDepartmentFilter = (value) => {
        setFilterDepartment(value);
    };

    const getFilteredData = () => {
        // 先过滤数据
        const filteredData = employees.filter(item => {
            // 搜索过滤
            if (searchText && !checkNameMatch(item.name, searchText) && !item.employeeId?.toLowerCase().includes(searchText.toLowerCase())) {
                return false;
            }

            // 如果选择了"全部部门"，则显示所有数据
            if (filterDepartment === 'all') {
                return true;
            }

            // 否则检查部门是否匹配（包含主部门和子部门）
            const fullDepartment = item.department ?
                `${item.department}${item.subDepartment ? ` - ${item.subDepartment}` : ''}`
                : '-';
            return fullDepartment === filterDepartment;
        });

        // 然后按工号排序（工号首字母和数字升序排列）
        return filteredData.sort((a, b) => {
            const idA = a.employeeId || '';
            const idB = b.employeeId || '';

            // 如果工号为空，则放到最后
            if (!idA) return 1;
            if (!idB) return -1;

            // 先按首字母排序
            const letterA = idA.charAt(0).toUpperCase();
            const letterB = idB.charAt(0).toUpperCase();

            if (letterA !== letterB) {
                return letterA.localeCompare(letterB);
            }

            // 如果首字母相同，则按数字部分排序
            const numA = parseInt(idA.substring(1)) || 0;
            const numB = parseInt(idB.substring(1)) || 0;

            return numA - numB;
        });
    };

    const handleConfirmDelete = async () => {
        if (!selectedEmployee) return;

        // 先关闭弹窗，避免 UI 阻塞
        setIsDeleteModalOpen(false);

        // 保存当前要删除的员工信息，避免状态丢失
        const employeeToDelete = { ...selectedEmployee };

        console.log(`尝试删除员工: ID=${employeeToDelete._id}, 工号=${employeeToDelete.employeeId}, 姓名=${employeeToDelete.name}`);

        try {
            // 添加时间戳参数，确保不使用缓存
            const timestamp = new Date().getTime();

            // 尝试使用ID删除
            let response = await fetch(`${config.apiBaseUrl}/employees/${employeeToDelete._id}?t=${timestamp}`, {
                method: 'DELETE',
                headers: {
                    'Authorization': `Bearer ${localStorage.getItem('token') || ''}`,
                    'Content-Type': 'application/json',
                    'Cache-Control': 'no-cache, no-store, must-revalidate',
                    'Pragma': 'no-cache',
                    'Expires': '0'
                }
            });

            // 如果通过ID删除失败，尝试通过工号删除
            if (!response.ok && response.status === 404 && employeeToDelete.employeeId) {
                console.log(`通过ID删除失败，尝试通过工号删除: ${employeeToDelete.employeeId}`);

                // 尝试使用工号删除
                response = await fetch(`${config.apiBaseUrl}/employees/${employeeToDelete.employeeId}?t=${timestamp}`, {
                    method: 'DELETE',
                    headers: {
                        'Authorization': `Bearer ${localStorage.getItem('token') || ''}`,
                        'Content-Type': 'application/json',
                        'Cache-Control': 'no-cache, no-store, must-revalidate',
                        'Pragma': 'no-cache',
                        'Expires': '0'
                    }
                });
            }

            if (!response.ok) {
                // 尝试解析错误信息
                let errorMessage = '删除失败';
                try {
                    const errorData = await response.json();
                    errorMessage = errorData.message || '删除失败';
                } catch (e) {
                    console.error('解析错误响应失败:', e);
                }

                // 如果是404错误（员工不存在），可能是因为数据库已经恢复或重置
                if (response.status === 404) {
                    console.log('员工不存在，可能是数据库已恢复或重置，尝试从本地列表中移除');

                    // 从本地列表中移除该员工
                    setEmployees(prev => prev.filter(emp =>
                        emp._id !== employeeToDelete._id &&
                        emp.employeeId !== employeeToDelete.employeeId
                    ));
                    message.warning(`员工 ${employeeToDelete.name} 已从列表中移除（可能已在数据库中删除）`);

                    // 刷新员工列表
                    setTimeout(() => {
                        fetchEmployees();
                    }, 500);
                } else {
                    throw new Error(errorMessage);
                }
            } else {
                // 解析响应
                try {
                    const responseData = await response.json();
                    console.log('删除成功，服务器响应:', responseData);
                } catch (e) {
                    console.warn('无法解析删除响应:', e);
                }

                // 使用 setTimeout 延迟更新状态，避免阻塞 UI
                setTimeout(() => {
                    // 更新本地状态，同时通过ID和工号过滤
                    setEmployees(prev => prev.filter(emp =>
                        emp._id !== employeeToDelete._id &&
                        emp.employeeId !== employeeToDelete.employeeId
                    ));
                    message.success(`${employeeToDelete.name} 已删除`);

                    // 刷新员工列表，确保数据同步
                    fetchEmployees();
                }, 100);
            }
        } catch (error) {
            console.error('删除失败:', error);
            message.error(`删除失败: ${error.message}`);

            // 如果删除失败，尝试刷新员工列表
            setTimeout(() => {
                fetchEmployees();
            }, 500);
        } finally {
            // 清理选中的员工
            setSelectedEmployee(null);
        }
    };

    const handleViewDetail = (employee) => {
        console.log(`尝试获取员工详情: ID=${employee._id}, 工号=${employee.employeeId}, 姓名=${employee.name}`);

        // 获取完整的员工信息
        // 添加时间戳参数，确保不使用缓存
        const timestamp = new Date().getTime();
        fetch(`${config.apiBaseUrl}/employees/${employee._id}?t=${timestamp}`, {
            headers: {
                'Authorization': `Bearer ${localStorage.getItem('token') || ''}`,
                'Content-Type': 'application/json',
                'Cache-Control': 'no-cache, no-store, must-revalidate',
                'Pragma': 'no-cache',
                'Expires': '0'
            }
        })
        .then(response => {
            if (!response.ok) {
                // 如果是404错误（员工不存在），可能是因为数据库已经恢复或重置
                if (response.status === 404) {
                    console.log('员工不存在，可能是数据库已恢复或重置，尝试刷新员工列表');
                    message.warning(`无法获取员工 ${employee.name} 的详情（可能已在数据库中删除）`);

                    // 刷新员工列表
                    setTimeout(() => {
                        fetchEmployees();
                    }, 500);

                    throw new Error('员工不存在，请刷新列表');
                }
                throw new Error('获取员工详情失败');
            }
            return response.json();
        })
        .then(data => {
            setDetailEmployee(data);
            setIsDetailModalVisible(true);
        })
        .catch(error => {
            console.error('获取员工详情失败:', error);
            if (!error.message.includes('员工不存在')) {
                message.error('获取员工详情失败: ' + error.message);
            }
        });
    };

    const handleEdit = (employee) => {
        console.log(`准备编辑员工: ID=${employee._id}, 工号=${employee.employeeId}, 姓名=${employee.name}`);

        // 先检查员工是否存在
        const timestamp = new Date().getTime();
        fetch(`${config.apiBaseUrl}/employees/${employee._id}?t=${timestamp}`, {
            headers: {
                'Authorization': `Bearer ${localStorage.getItem('token') || ''}`,
                'Content-Type': 'application/json',
                'Cache-Control': 'no-cache, no-store, must-revalidate'
            }
        })
        .then(response => {
            if (!response.ok) {
                // 如果是404错误（员工不存在），可能是因为数据库已经恢复或重置
                if (response.status === 404) {
                    console.log('员工不存在，可能是数据库已恢复或重置，尝试刷新员工列表');
                    message.warning(`无法编辑员工 ${employee.name}（可能已在数据库中删除）`);

                    // 刷新员工列表
                    setTimeout(() => {
                        fetchEmployees();
                    }, 500);

                    throw new Error('员工不存在，请刷新列表');
                }
                throw new Error('获取员工信息失败');
            }
            // 员工存在，导航到编辑页面
            navigate(`/employeeList/edit/${employee._id}`);
        })
        .catch(error => {
            console.error('编辑前检查员工失败:', error);
            if (!error.message.includes('员工不存在')) {
                message.error('编辑失败: ' + error.message);
            }
        });
    };

    const handleDelete = (employee) => {
        // 直接使用 Modal 组件，不使用原生 window.confirm
        setSelectedEmployee(employee);

        // 使用 requestAnimationFrame 确保在下一帧渲染时打开弹窗，避免阻塞 UI
        requestAnimationFrame(() => {
            setIsDeleteModalOpen(true);
        });
    };

    // 添加职称级别显示函数 - 支持新的数组数据结构
    const getTitleLevelDisplay = (employee) => {
        const titles = [];

        // 处理新的数组数据结构
        if (employee.titleLevels && Array.isArray(employee.titleLevels)) {
            // 添加基础职称（无、初级、中级、高级）
            const basicTitles = employee.titleLevels.filter(level => ['无', '初级', '中级', '高级'].includes(level));

            // 检查是否有执业资格
            const hasQualifications = employee.titleLevels.includes('执业资格') &&
                                     employee.professionalQualifications &&
                                     employee.professionalQualifications.length > 0;

            // 如果只有执业资格而没有基础职称，不显示"无"
            if (basicTitles.includes('无') && hasQualifications) {
                // 有执业资格时，不显示"无"
            } else {
                // 添加基础职称
                titles.push(...basicTitles);
            }

            // 添加执业资格
            if (hasQualifications) {
                employee.professionalQualifications.forEach((qual, index) => {
                    if (qual === '注册安全工程师') {
                        const level = employee.safetyEngineerLevels && employee.safetyEngineerLevels[index];
                        if (level) {
                            titles.push(`${qual}（${level}）`);
                        } else {
                            titles.push(qual);
                        }
                    } else if (qual === '注册建造师') {
                        const level = employee.constructorLevels && employee.constructorLevels[index];
                        const specialty = employee.constructorSpecialties && employee.constructorSpecialties[index];
                        if (level) {
                            const specialtyText = specialty ? specialty : '';
                            titles.push(`${qual}（${level}${specialtyText}）`);
                        } else {
                            titles.push(qual);
                        }
                    } else {
                        titles.push(qual);
                    }
                });
            }

            return titles.length > 0 ? titles.join('、') : '-';
        }

        // 兼容旧的数据结构
        if (employee.titleLevel === '执业资格') {
            if (employee.professionalQualification === '注册安全工程师') {
                return `${employee.professionalQualification}（${employee.safetyEngineerLevel}）`;
            }
            if (employee.professionalQualification === '注册建造师') {
                return `${employee.professionalQualification}（${employee.constructorLevel}${employee.constructorSpecialty}）`;
            }
            return employee.professionalQualification || '-';
        }
        return employee.titleLevel || '-';
    };

    // 在 handleExportWithFormat 函数中也需要修改相应的导出逻辑
    const handleExportWithFormat = (fileName, fileFormat) => {
        // 使用已经排序的数据进行导出，保持与表格显示一致
        const data = getFilteredData();

        // 创建一个新的数据数组，其中职称级别字段已经被处理
        const processedData = data.map(emp => {
            // 创建一个新对象，避免修改原始数据
            const newEmp = { ...emp };
            // 将职称级别替换为正确的显示值
            newEmp.titleLevel = getTitleLevelDisplay(emp);
            // 添加完整部门信息
            newEmp.department = emp.department ?
                `${emp.department}${emp.subDepartment ? ` - ${emp.subDepartment}` : ''}` :
                '-';
            // 添加备注字段
            newEmp.备注 = '';
            return newEmp;
        });

        const options = {
            title: '员工花名册',
            headers: ['工号', '姓名', '性别', '所属部门', '岗位名称', '管理岗位级别', '职称级别', '入职时间', '工作性质', '在职状态', '备注'],
            fields: ['employeeId', 'name', 'gender', 'department', 'position', 'administrativeLevel', 'titleLevel', 'entryDate', 'workType', 'status', '备注']
        };

        switch (fileFormat) {
            case 'csv': {
                const headers = ['工号', '姓名', '性别', '所属部门', '岗位名称', '管理岗位级别', '职称级别', '入职时间', '工作性质', '在职状态', '备注'];
                const csvContent = [
                    headers.join(','),
                    ...processedData.map(emp => [
                        emp.employeeId || '',
                        emp.name || '',
                        emp.gender || '',
                        emp.department || '',
                        emp.position || '',
                        emp.administrativeLevel || '',
                        emp.titleLevel || '',
                        emp.entryDate || '',
                        emp.workType || '',
                        emp.status || '',
                        emp.备注 || ''
                    ].join(','))
                ].join('\n');

                const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
                const link = document.createElement('a');
                link.href = URL.createObjectURL(blob);
                link.download = `${fileName}.${fileFormat}`;
                link.click();
                break;
            }
            case 'xlsx':
            case 'xls': {
                ExportUtils.exportAsExcel(processedData, fileName, options);
                break;
            }
            case 'pdf': {
                ExportUtils.exportAsPDF(processedData, fileName, options);
                break;
            }
            case 'doc':
            case 'docx': {
                ExportUtils.exportAsWord(processedData, fileName, options);
                break;
            }
            default:
                console.error('不支持的文件格式');
        }
    };

    return (
        <div className="employees-container">
            <div className="content-wrapper">
                <h2 style={{ textAlign: 'center', margin: '20px 0' }}>员工花名册</h2>
                <div className="action-filter-bar">
                    <div className="filter-area">
                        <div className="search-group">
                            <label>搜索：</label>
                            <Input
                                placeholder="姓名/拼音首字母/工号"
                                allowClear
                                onChange={(e) => setSearchText(e.target.value)}
                                style={{ width: 168, borderRadius: '6px', height: '32px' }}
                                className="employee-search-input"
                            />
                        </div>
                        <div className="department-group">
                            <label>部门：</label>
                            <Select
                                placeholder="选择部门"
                                onChange={handleDepartmentFilter}
                                style={{ width: 130, borderRadius: '6px' }}
                                defaultValue="all"
                                showSearch
                                optionFilterProp="children"
                                className="employee-department-select"
                                dropdownStyle={{
                                    maxHeight: 400,
                                    overflow: 'auto',
                                    borderRadius: '6px',
                                    boxShadow: '0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 9px 28px 8px rgba(0, 0, 0, 0.05)'
                                }}
                                listHeight={400}
                            >
                                <Select.Option value="all" style={{ fontWeight: 'bold', borderBottom: '1px solid #f0f0f0', padding: '8px 12px', backgroundColor: '#f0f7ff' }}>
                                    全部部门
                                </Select.Option>
                                {Array.from(new Set(employees.map(item => {
                                    const fullDepartment = item.department ? `${item.department}${item.subDepartment ? ` - ${item.subDepartment}` : ''}` : '-';
                                    return fullDepartment;
                                }))).map(dept => (
                                    <Select.Option key={dept} value={dept} style={{ padding: '8px 12px', borderBottom: '1px solid #f5f5f5' }}>{dept}</Select.Option>
                                ))}
                            </Select>
                        </div>
                    </div>
                    <div className="action-buttons-group">
                        <button className="action-button back-button" onClick={() => navigate('/dashboard')}>
                            <i className="button-icon">←</i>
                            返回
                        </button>
                        <button
                            className="action-button export-button"
                            onClick={() => setIsExportDialogOpen(true)}
                        >
                            <i className="button-icon">↓</i>
                            导出
                        </button>
                        <button
                            className="action-button print-button"
                            onClick={() => {
                                // 准备打印数据
                                const printData = getFilteredData();
                                // 准备打印列配置
                                const printColumns = [
                                    { title: '工号', dataIndex: 'employeeId', key: 'employeeId' },
                                    { title: '姓名', dataIndex: 'name', key: 'name' },
                                    { title: '性别', dataIndex: 'gender', key: 'gender' },
                                    {
                                        title: '所属部门',
                                        key: 'department',
                                        render: (_, record) => (
                                            record.department ?
                                            `${record.department}${record.subDepartment ? ` - ${record.subDepartment}` : ''}`
                                            : '-'
                                        )
                                    },
                                    { title: '岗位名称', dataIndex: 'position', key: 'position' },
                                    { title: '管理级别', dataIndex: 'administrativeLevel', key: 'administrativeLevel' },
                                    {
                                        title: '职称级别',
                                        key: 'titleLevel',
                                        render: (_, record) => getTitleLevelDisplay(record)
                                    },
                                    { title: '入职时间', dataIndex: 'entryDate', key: 'entryDate' },
                                    { title: '工作性质', dataIndex: 'workType', key: 'workType' },
                                    { title: '在职状态', dataIndex: 'status', key: 'status' },
                                    { title: '备注', dataIndex: '备注', key: '备注', render: () => '' }
                                ];

                                // 调用打印工具
                                PrintUtils.printContent(
                                    '员工花名册',
                                    printData,
                                    printColumns,
                                    {
                                        '员工总数': printData.length,
                                        '在职人数': printData.filter(emp => emp.status === '在职').length,
                                        '离职人数': printData.filter(emp => emp.status === '离职').length
                                    },
                                    { companyName: '人力资源管理系统' }
                                );
                            }}
                        >
                            <i className="button-icon">🖨</i>
                            打印
                        </button>
                        <button
                            className="action-button reminder-button"
                            onClick={() => setProbationReminderVisible(true)}
                            style={{
                                backgroundColor: probationCount > 0 ? '#ff4d4f' : '#1890ff',
                                position: 'relative'
                            }}
                        >
                            <BellOutlined style={{ marginRight: 4 }} />
                            试用期提醒
                            {probationCount > 0 && (
                                <span style={{
                                    position: 'absolute',
                                    top: -8,
                                    right: -8,
                                    backgroundColor: '#fff',
                                    color: '#ff4d4f',
                                    borderRadius: '50%',
                                    width: 20,
                                    height: 20,
                                    fontSize: 12,
                                    display: 'flex',
                                    alignItems: 'center',
                                    justifyContent: 'center',
                                    fontWeight: 'bold',
                                    border: '2px solid #ff4d4f'
                                }}>
                                    {probationCount}
                                </span>
                            )}
                        </button>
                        <button className="action-button add-button" onClick={() => navigate('/employeeList/add')}>
                            <i className="button-icon">+</i>
                            添加员工
                        </button>
                    </div>
                </div>

                <div className="table-container">
                    <Table
                        columns={[
                        {
                            title: '工号 ↑', // 添加升序排列的指示符号
                            dataIndex: 'employeeId',
                            key: 'employeeId',
                            align: 'center',
                            sorter: false, // 禁用点击排序，因为我们已经在 getFilteredData 中实现了排序
                            sortDirections: ['ascend'], // 只显示升序图标
                            defaultSortOrder: 'ascend', // 默认升序
                            filteredValue: null, // 禁用内置筛选
                            filterDropdown: null // 禁用内置筛选下拉框
                        },
                        { title: '姓名', dataIndex: 'name', key: 'name', align: 'center', filteredValue: null, filterDropdown: null },
                        { title: '性别', dataIndex: 'gender', key: 'gender', align: 'center', filteredValue: null, filterDropdown: null },
                        {
                            title: '所属部门',
                            key: 'department',
                            align: 'center',
                            filteredValue: null,
                            filterDropdown: null,
                            render: (_, record) => (
                                record.department ?
                                `${record.department}${record.subDepartment ? ` - ${record.subDepartment}` : ''}`
                                : '-'
                            )
                        },
                        { title: '岗位名称', dataIndex: 'position', key: 'position', align: 'center', filteredValue: null, filterDropdown: null },
                        { title: '管理级别', dataIndex: 'administrativeLevel', key: 'administrativeLevel', align: 'center', filteredValue: null, filterDropdown: null },
                        {
                            title: '职称级别',
                            key: 'titleLevel',
                            align: 'center',
                            filteredValue: null,
                            filterDropdown: null,
                            render: (_, record) => getTitleLevelDisplay(record)
                        },
                        { title: '入职时间', dataIndex: 'entryDate', key: 'entryDate', align: 'center', filteredValue: null, filterDropdown: null },
                        { title: '工作性质', dataIndex: 'workType', key: 'workType', align: 'center', filteredValue: null, filterDropdown: null },
                        {
                            title: '在职状态',
                            key: 'status',
                            align: 'center',
                            filteredValue: null,
                            filterDropdown: null,
                            render: (_, record) => (
                                <span className={`status-badge ${record.status === '在职' ? 'active' : 'inactive'}`}>
                                    {record.status || '-'}
                                </span>
                            )
                        },
                        {
                            title: '操作',
                            key: 'action',
                            align: 'center',
                            width: 150,
                            filteredValue: null,
                            filterDropdown: null,
                            render: (_, record) => (
                                <div className="action-buttons-container">
                                    <span
                                        className="text-link-common success"
                                        onClick={() => handleViewDetail(record)}
                                    >
                                        <FileSearchOutlined style={{ marginRight: '4px' }} />
                                        详情
                                    </span>
                                    <span
                                        className="text-link-common"
                                        onClick={() => handleEdit(record)}
                                    >
                                        <EditOutlined style={{ marginRight: '4px' }} />
                                        编辑
                                    </span>
                                    <span
                                        className="text-link-common danger"
                                        onClick={() => handleDelete(record)}
                                    >
                                        <DeleteOutlined style={{ marginRight: '4px' }} />
                                        删除
                                    </span>
                                </div>
                            )
                        }
                    ]}
                    dataSource={getFilteredData()}
                    rowKey="_id"
                    scroll={{ x: 'max-content' }}
                    bordered
                    size="small"
                    tableLayout="fixed"
                    showSorterTooltip={false}
                    showFilter={false}
                    filterSearch={false}
                        pagination={{
                        position: ['bottomCenter'],
                        showSizeChanger: true,
                        showQuickJumper: true,
                        style: { padding: '16px 0' },
                        total: getFilteredData().length,
                        pageSize: pageSize,
                        onChange: (page, size) => {
                            if (size !== pageSize) {
                                setPageSize(size);
                            }
                        },
                        showTotal: (total) => (
                            <span>
                                第 <Input
                                    style={{
                                        width: '45px',
                                        margin: '0 8px',
                                        textAlign: 'center'
                                    }}
                                    onPressEnter={(e) => {
                                        const page = parseInt(e.target.value);
                                        if (page && page > 0 && page <= Math.ceil(total / pageSize)) {
                                            e.target.blur();
                                        }
                                    }}
                                /> 页/共 {Math.ceil(total / pageSize)} 页
                            </span>
                        ),
                        itemRender: (_, type) => {
                            if (type === 'prev') return <button className="page-button">上一页</button>;
                            if (type === 'next') return <button className="page-button">下一页</button>;
                            return null;
                        }
                    }}
                    />
                </div>

                <Modal
                    open={isDeleteModalOpen}
                    onCancel={() => {
                        // 使用 requestAnimationFrame 确保在下一帧渲染时关闭弹窗，避免阻塞 UI
                        requestAnimationFrame(() => {
                            setIsDeleteModalOpen(false);
                            // 延迟清理状态，避免状态更新冲突
                            setTimeout(() => {
                                setSelectedEmployee(null);
                            }, 100);
                        });
                    }}
                    onOk={handleConfirmDelete}
                    title={<span className="delete-modal-title">⚠️删除确认❗️</span>}
                    okText="确认删除"
                    cancelText="取消"
                    okButtonProps={{danger: true}}
                    centered
                    maskClosable={false}
                    destroyOnClose={true}
                    className="employee-delete-modal"
                    styles={{
                        mask: { backgroundColor: 'rgba(0,0,0,0.45)' },
                        wrapper: { zIndex: 1000 }
                    }}
                    footer={
                        <div className="delete-modal-footer">
                            <Button
                                onClick={(e) => {
                                    // 阻止事件冒泡，避免与其他事件冲突
                                    e.stopPropagation();
                                    // 使用 requestAnimationFrame 确保在下一帧渲染时关闭弹窗，避免阻塞 UI
                                    requestAnimationFrame(() => {
                                        setIsDeleteModalOpen(false);
                                        // 延迟清理状态，避免状态更新冲突
                                        setTimeout(() => {
                                            setSelectedEmployee(null);
                                        }, 100);
                                    });
                                }}
                                style={{
                                    height: '28px',
                                    width: '86px',
                                    fontSize: '12px',
                                    borderRadius: '4px',
                                    fontWeight: 500
                                }}
                            >
                                取消
                            </Button>
                            <Button
                                type="primary"
                                danger
                                onClick={(e) => {
                                    // 阻止事件冒泡，避免与其他事件冲突
                                    e.stopPropagation();
                                    // 使用 setTimeout 延迟处理，避免阻塞 UI
                                    setTimeout(() => {
                                        handleConfirmDelete();
                                    }, 0);
                                }}
                                style={{
                                    height: '28px',
                                    width: '86px',
                                    fontSize: '12px',
                                    borderRadius: '4px',
                                    boxShadow: '0 2px 6px rgba(255, 77, 79, 0.2)',
                                    fontWeight: 500
                                }}
                            >
                                确认删除
                            </Button>
                        </div>
                    }
                >
                    <div className="delete-modal-content" style={{textAlign: 'center'}}>
                        {selectedEmployee && (
                            <>
                                <p style={{fontSize: '16px'}}>确定要删除员工 {selectedEmployee.name} (工号: {selectedEmployee.employeeId}) 吗？</p>
                                <p style={{fontSize: '16px'}}>此操作不可恢复！</p>
                            </>
                        )}
                    </div>
                </Modal>


                <ExportDialog
                    isOpen={isExportDialogOpen}
                    onClose={() => setIsExportDialogOpen(false)}
                    onExport={handleExportWithFormat}
                    moduleType="员工详情"
                />

                {/* 员工详情弹窗 */}
                {detailEmployee && (
                    <EmployeeDetails
                        visible={isDetailModalVisible}
                        employee={detailEmployee}
                        onClose={() => setIsDetailModalVisible(false)}
                    />
                )}

                {/* 试用期提醒弹窗 */}
                <ProbationReminder
                    visible={probationReminderVisible}
                    onClose={() => setProbationReminderVisible(false)}
                    onRefresh={() => {
                        fetchEmployees();
                        checkProbationReminders();
                    }}
                />
            </div>
        </div>
    );
}

export default EmployeeList;
