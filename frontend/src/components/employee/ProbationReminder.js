import React, { useState, useEffect } from 'react';
import { Modal, Table, Button, Space, Tag, message, Form, Input, DatePicker, Select } from 'antd';
import { ExclamationCircleOutlined, CheckCircleOutlined, ClockCircleOutlined } from '@ant-design/icons';
import moment from 'moment';
import config from '../../config';

const { TextArea } = Input;
const { Option } = Select;

const ProbationReminder = ({ visible, onClose, onRefresh }) => {
    const [loading, setLoading] = useState(false);
    const [probationEmployees, setProbationEmployees] = useState([]);
    const [confirmModalVisible, setConfirmModalVisible] = useState(false);
    const [selectedEmployee, setSelectedEmployee] = useState(null);
    const [form] = Form.useForm();

    // 获取即将到期的试用期员工
    const fetchProbationEmployees = async () => {
        setLoading(true);
        try {
            const response = await fetch(`${config.apiBaseUrl}/employees/probation-reminders`, {
                headers: {
                    'Authorization': `Bearer ${localStorage.getItem('token')}`,
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                throw new Error('获取试用期员工数据失败');
            }

            const data = await response.json();
            setProbationEmployees(data);
        } catch (error) {
            console.error('获取试用期员工失败:', error);
            message.error('获取试用期员工数据失败');
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        if (visible) {
            fetchProbationEmployees();
        }
    }, [visible]);

    // 计算剩余天数
    const calculateRemainingDays = (endDate) => {
        const today = moment();
        const end = moment(endDate);
        return end.diff(today, 'days');
    };

    // 获取状态标签
    const getStatusTag = (employee) => {
        const remainingDays = calculateRemainingDays(employee.probationEndDate);
        
        if (remainingDays < 0) {
            return <Tag color="red" icon={<ExclamationCircleOutlined />}>已超期 {Math.abs(remainingDays)} 天</Tag>;
        } else if (remainingDays === 0) {
            return <Tag color="orange" icon={<ClockCircleOutlined />}>今日到期</Tag>;
        } else if (remainingDays <= 7) {
            return <Tag color="gold" icon={<ClockCircleOutlined />}>还有 {remainingDays} 天到期</Tag>;
        } else {
            return <Tag color="blue">还有 {remainingDays} 天</Tag>;
        }
    };

    // 处理转正确认
    const handleConfirmRegularization = (employee) => {
        setSelectedEmployee(employee);
        form.setFieldsValue({
            regularizationDate: moment(),
            newPosition: employee.position,
            newDepartment: employee.department,
            newSubDepartment: employee.subDepartment,
            remarks: ''
        });
        setConfirmModalVisible(true);
    };

    // 提交转正确认
    const submitRegularization = async () => {
        try {
            const values = await form.validateFields();
            setLoading(true);

            const response = await fetch(`${config.apiBaseUrl}/employees/${selectedEmployee._id}/regularization`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${localStorage.getItem('token')}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    regularizationDate: values.regularizationDate.format('YYYY-MM-DD'),
                    newPosition: values.newPosition,
                    newDepartment: values.newDepartment,
                    newSubDepartment: values.newSubDepartment,
                    remarks: values.remarks
                })
            });

            if (!response.ok) {
                throw new Error('转正确认失败');
            }

            message.success(`${selectedEmployee.name} 转正确认成功！`);
            setConfirmModalVisible(false);
            form.resetFields();
            setSelectedEmployee(null);
            
            // 刷新数据
            fetchProbationEmployees();
            if (onRefresh) {
                onRefresh();
            }
        } catch (error) {
            console.error('转正确认失败:', error);
            message.error('转正确认失败，请重试');
        } finally {
            setLoading(false);
        }
    };

    const columns = [
        {
            title: '员工编号',
            dataIndex: 'employeeId',
            key: 'employeeId',
            width: 100
        },
        {
            title: '姓名',
            dataIndex: 'name',
            key: 'name',
            width: 100
        },
        {
            title: '部门',
            dataIndex: 'department',
            key: 'department',
            width: 120
        },
        {
            title: '岗位',
            dataIndex: 'position',
            key: 'position',
            width: 120
        },
        {
            title: '入职日期',
            dataIndex: 'entryDate',
            key: 'entryDate',
            width: 110
        },
        {
            title: '试用期截止',
            dataIndex: 'probationEndDate',
            key: 'probationEndDate',
            width: 110
        },
        {
            title: '状态',
            key: 'status',
            width: 150,
            render: (_, employee) => getStatusTag(employee)
        },
        {
            title: '操作',
            key: 'actions',
            width: 120,
            render: (_, employee) => (
                <Space>
                    <Button 
                        type="primary" 
                        size="small"
                        icon={<CheckCircleOutlined />}
                        onClick={() => handleConfirmRegularization(employee)}
                    >
                        确认转正
                    </Button>
                </Space>
            )
        }
    ];

    return (
        <>
            <Modal
                title={
                    <div style={{ display: 'flex', alignItems: 'center' }}>
                        <ExclamationCircleOutlined style={{ color: '#faad14', marginRight: 8 }} />
                        试用期到期提醒
                    </div>
                }
                open={visible}
                onCancel={onClose}
                footer={[
                    <Button key="close" onClick={onClose}>
                        关闭
                    </Button>
                ]}
                width={1000}
                destroyOnClose
            >
                <div style={{ marginBottom: 16 }}>
                    <p style={{ color: '#666', margin: 0 }}>
                        以下员工的试用期即将到期或已超期，请及时处理转正事宜：
                    </p>
                </div>
                
                <Table
                    columns={columns}
                    dataSource={probationEmployees}
                    rowKey="_id"
                    loading={loading}
                    pagination={false}
                    scroll={{ x: 800 }}
                    size="small"
                />
                
                {probationEmployees.length === 0 && !loading && (
                    <div style={{ textAlign: 'center', padding: '40px 0', color: '#999' }}>
                        <CheckCircleOutlined style={{ fontSize: 48, marginBottom: 16 }} />
                        <p>暂无即将到期的试用期员工</p>
                    </div>
                )}
            </Modal>

            {/* 转正确认弹窗 */}
            <Modal
                title="确认员工转正"
                open={confirmModalVisible}
                onOk={submitRegularization}
                onCancel={() => {
                    setConfirmModalVisible(false);
                    form.resetFields();
                    setSelectedEmployee(null);
                }}
                confirmLoading={loading}
                width={600}
            >
                {selectedEmployee && (
                    <Form
                        form={form}
                        layout="vertical"
                        initialValues={{
                            regularizationDate: moment(),
                            newPosition: selectedEmployee.position,
                            newDepartment: selectedEmployee.department,
                            newSubDepartment: selectedEmployee.subDepartment
                        }}
                    >
                        <div style={{ marginBottom: 16, padding: 12, backgroundColor: '#f5f5f5', borderRadius: 4 }}>
                            <p><strong>员工信息：</strong>{selectedEmployee.name} ({selectedEmployee.employeeId})</p>
                            <p><strong>当前部门：</strong>{selectedEmployee.department}</p>
                            <p><strong>当前岗位：</strong>{selectedEmployee.position}</p>
                            <p><strong>试用期截止：</strong>{selectedEmployee.probationEndDate}</p>
                        </div>

                        <Form.Item
                            name="regularizationDate"
                            label="转正日期"
                            rules={[{ required: true, message: '请选择转正日期' }]}
                        >
                            <DatePicker style={{ width: '100%' }} />
                        </Form.Item>

                        <Form.Item
                            name="newPosition"
                            label="转正后岗位"
                            rules={[{ required: true, message: '请输入转正后岗位' }]}
                        >
                            <Input placeholder="请输入转正后岗位" />
                        </Form.Item>

                        <Form.Item
                            name="newDepartment"
                            label="转正后部门"
                            rules={[{ required: true, message: '请输入转正后部门' }]}
                        >
                            <Input placeholder="请输入转正后部门" />
                        </Form.Item>

                        <Form.Item
                            name="newSubDepartment"
                            label="转正后子部门"
                        >
                            <Input placeholder="请输入转正后子部门（可选）" />
                        </Form.Item>

                        <Form.Item
                            name="remarks"
                            label="备注"
                        >
                            <TextArea 
                                rows={3} 
                                placeholder="请输入转正备注信息（可选）" 
                                maxLength={200}
                                showCount
                            />
                        </Form.Item>
                    </Form>
                )}
            </Modal>
        </>
    );
};

export default ProbationReminder;
