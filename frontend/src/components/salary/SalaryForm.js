import React, { useState, useEffect, useRef } from 'react';
import { Form, Select, Modal, message, Input, Button, Space, Row, Col, InputNumber, Typography, Popover} from 'antd';
import { QuestionCircleOutlined } from '@ant-design/icons';
import Draggable from 'react-draggable';
import './SalaryForm.css';
import SalaryService from './SalaryService';
import { 
    formatCurrency,
    safeNumber, 
    isProbationPeriod, 
    getPerformanceLevelName as utilGetPerformanceLevelName,
    getPerformanceCoefficient as utilGetPerformanceCoefficient,
    getDefaultSalaryData,
    getPositionType,
    getEducationCoefficient,
    calculateEducationAdjustment,
    getLanguageCoefficient,
    calculateLanguageAdjustment,
    mapEducation
} from '../../utils/salaryUtils';
import PositionLevelHelp from '../common/PositionLevelHelp';

// 绩效等级说明组件
const PerformanceLevelHelp = () => {
    const { Text, Title } = Typography;

    return (
        <div className="performance-level-help">
            <div className="help-section">
                <Title level={5}>绩效等级评定依据</Title>
                <ul>
                    <li>
                        <Text strong>A（优秀）:</Text>
                        <div>
                            <p>工作表现远超预期，在所有关键绩效指标上均表现卓越。</p>
                            <p>主动承担额外责任，提出创新解决方案，对团队和公司有显著贡献。</p>
                            <p>系数: 1.3-1.5</p>
                        </div>
                    </li>
                    <li>
                        <Text strong>B+（良好）:</Text>
                        <div>
                            <p>工作表现超过预期，在大多数关键绩效指标上表现良好。</p>
                            <p>能够独立完成任务，并有一定的创新能力，对团队有积极贡献。</p>
                            <p>系数: 1.1-1.2</p>
                        </div>
                    </li>
                    <li>
                        <Text strong>B（合格）:</Text>
                        <div>
                            <p>工作表现符合预期，能够完成所有基本工作要求。</p>
                            <p>按时完成分配的任务，工作质量达到标准要求。</p>
                            <p>系数: 1.0</p>
                        </div>
                    </li>
                    <li>
                        <Text strong>C（待改进）:</Text>
                        <div>
                            <p>工作表现低于预期，在某些关键绩效指标上未达标准。</p>
                            <p>需要改进工作质量或效率，有时需要额外指导才能完成任务。</p>
                            <p>系数: 0.8-0.9</p>
                        </div>
                    </li>
                    <li>
                        <Text strong>D（不合格）:</Text>
                        <div>
                            <p>工作表现远低于预期，在多个关键绩效指标上表现不佳。</p>
                            <p>无法独立完成基本工作任务，工作质量或效率严重不足。</p>
                            <p>系数: 0.5-0.7</p>
                        </div>
                    </li>
                </ul>
            </div>

            <div className="help-section">
                <Title level={5}>绩效评定周期</Title>
                <p>绩效评定通常每季度进行一次，年终进行年度综合评定。</p>
                <p>绩效系数直接影响员工的绩效奖金计算。</p>
            </div>
        </div>
    );
};

const SalaryForm = ({ visible = false, onCancel, onOk, initialValues = {} }) => {
    const [disabled, setDisabled] = useState(true);
    const [form] = Form.useForm();
    const [positionLevels, setPositionLevels] = useState([]);
    const [performanceLevels, setPerformanceLevels] = useState([]);
    const [performanceCoefficients, setPerformanceCoefficients] = useState({});
    const [salaryResult, setSalaryResult] = useState(null);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState(null);
    const [config, setConfig] = useState(null);
    const [helpVisible, setHelpVisible] = useState(false);

    const [isProbation, setIsProbation] = useState(false);
    const [positionType, setPositionType] = useState('');
    const [defaultPositionLevel, setDefaultPositionLevel] = useState('D1');
    
    const [isFormReady, setIsFormReady] = useState(false);
    const dragRef = useRef(null);

    // 加载配置和设置岗位等级选项
    useEffect(() => {
        let isMounted = true;  // 添加标志来跟踪组件是否已卸载

        async function loadConfig() {
            if (!visible) return;  // 如果表单不可见，不加载配置

            try {
                const salaryConfig = await SalaryService.getSalaryConfig();
                if (!isMounted) return;  // 如果组件已卸载，不更新状态

                setConfig(salaryConfig);
                console.log('薪资表单加载最新配置:', {
                    BASE_SALARY: salaryConfig.SALARY_CONFIG?.BASE_SALARY,
                    MEAL_ALLOWANCE: salaryConfig.SALARY_CONFIG?.MEAL_ALLOWANCE,
                    COMMUNICATION_ALLOWANCE: salaryConfig.SALARY_CONFIG?.COMMUNICATION_ALLOWANCE,
                    INSURANCE_RATES: salaryConfig.SALARY_CONFIG?.INSURANCE_RATES,
                    PERFORMANCE_LEVELS: salaryConfig.SALARY_CONFIG?.PERFORMANCE_LEVELS
                });

                // 确定岗位类型
                const type = getPositionType(initialValues, salaryConfig);
                setPositionType(type);
                console.log('确定岗位类型:', type);

                // 根据岗位类型设置默认岗位等级
                let level = 'D1';
                if (type === '技术') {
                    level = 'A1';
                } else if (type === '高管') {
                    level = 'B1';
                } else if (type === '支持') {
                    level = 'C1';
                }
                setDefaultPositionLevel(level);

                // 根据岗位类型设置对应的岗位等级选项
                let levels = [];
                if (type === '技术') {
                    levels = Array.from({ length: 20 }, (_, i) => `A${i + 1}`);
                } else if (type === '高管') {
                    levels = Array.from({ length: 4 }, (_, i) => `B${i + 1}`);
                } else if (type === '支持') {
                    levels = Array.from({ length: 15 }, (_, i) => `C${i + 1}`);
                } else if (type === '其他') {
                    levels = Array.from({ length: 15 }, (_, i) => `D${i + 1}`);
                }
                
                console.log('设置岗位等级选项:', levels);
                console.log('岗位类型:', type, '默认等级:', level);
                setPositionLevels(levels);

                if (salaryConfig && salaryConfig.PERFORMANCE_LEVELS) {
                    setPerformanceLevels(salaryConfig.PERFORMANCE_LEVELS);
                    if (salaryConfig.SALARY_CONFIG && salaryConfig.SALARY_CONFIG.PERFORMANCE_LEVELS) {
                        setPerformanceCoefficients(salaryConfig.SALARY_CONFIG.PERFORMANCE_LEVELS);
                        console.log('加载绩效系数:', salaryConfig.SALARY_CONFIG.PERFORMANCE_LEVELS);
                    }
                }
            } catch (error) {
                console.error('加载薪资配置失败:', error);
                message.error('加载配置失败');
            }
        }

        loadConfig();

        // 清理函数
        return () => {
            isMounted = false;  // 标记组件已卸载
        };
    }, [visible, initialValues]);  // 只在 visible 或 initialValues 变化时重新加载配置

    // 单独处理表单值的设置
    useEffect(() => {
        const initializeForm = async () => {
            if (!config || !initialValues) return;
            
            // 自动获取考勤数据（高管除外）
            let attendanceData = null;
            if (initialValues.employeeId || initialValues.name) {
                // 判断是否为高管
                const isExecutive = positionType === '高管';

                if (!isExecutive) {
                    try {
                        attendanceData = await fetchAttendanceData(initialValues.employeeId, initialValues.name);
                        if (attendanceData) {
                            console.log('自动获取到考勤数据:', attendanceData);
                            message.success(`已自动获取考勤数据：实际出勤${attendanceData.actualAttendDays}天`);
                        } else {
                            console.log('未获取到考勤数据，使用默认值0');
                        }
                    } catch (error) {
                        console.warn('自动获取考勤数据失败:', error);
                    }
                } else {
                    console.log('高管不参与考勤计算，实际出勤天数将与额定出勤天数同步');
                }
            }
            // 判断初始值中的岗位等级是否与当前岗位类型匹配
            let finalPositionLevel = defaultPositionLevel;
            if (initialValues.positionLevel) {
                const levelPrefix = initialValues.positionLevel.charAt(0);
                const expectedPrefix = positionType === '技术' ? 'A' : 
                    (positionType === '高管' ? 'B' : 
                    (positionType === '支持' ? 'C' : 'D'));
                
                // 如果岗位等级的前缀与岗位类型匹配，则保留原值
                if (levelPrefix === expectedPrefix) {
                    finalPositionLevel = initialValues.positionLevel;
                } else {
                    console.log('岗位等级不匹配，使用默认值。原值:', initialValues.positionLevel, '新值:', defaultPositionLevel);
                }
            }
            
            // 获取学历有效性判断结果
            let education = initialValues.education || '本科';
            let educationType = '全日制'; // 默认为全日制
            
            // 检查是否有学历类型信息
            if (initialValues.firstEducationType || initialValues.finalEducationType) {
                // 使用getHighestEducation函数判断有效学历
                const educationResult = getHighestEducation(initialValues);
                if (educationResult.level) {
                    education = educationResult.level;
                }
                
                // 判断学历类型
                const isFinalFullTime = initialValues.finalEducationType === '全日制';
                const isFirstFullTime = initialValues.firstEducationType === '全日制';
                
                if (!isFinalFullTime && !isFirstFullTime) {
                    educationType = '非全日制';
                }
                
                console.log('学历判断结果:', {
                    education,
                    educationType,
                    firstEducationType: initialValues.firstEducationType,
                    finalEducationType: initialValues.finalEducationType
                });
            }
            
            const formValues = {
                ...initialValues,
                positionType: positionType,
                education: education,
                educationType: educationType, // 添加学历类型字段
                languageLevel: initialValues.languageLevel || '未知',
                administrativeLevel: initialValues.administrativeLevel || '无',
                performanceLevel: initialValues.performanceLevel || 'B',
                performanceCoefficient: initialValues.performanceCoefficient || 1.0,
                // 使用计算出的岗位等级
                positionLevel: finalPositionLevel,
                department: initialValues.department || '',
                subDepartment: initialValues.subDepartment,
                specialAllowance: initialValues.specialAllowance || { remark: '', amount: 0 },
                specialDeduction: initialValues.specialDeduction || { amount: 0 },
                isProbation: initialValues.isProbation || false,
                actualAttendance: (() => {
                    // 高管的实际出勤天数应该与额定出勤天数同步
                    if (positionType === '高管') {
                        // 如果有薪资数据中的额定出勤天数，直接使用
                        if (initialValues.requiredAttendance && initialValues.requiredAttendance > 0) {
                            console.log(`高管${initialValues.name}的实际出勤天数设置为额定出勤天数: ${initialValues.requiredAttendance}天`);
                            return initialValues.requiredAttendance;
                        }

                        // 如果没有额定出勤天数，计算当前月份的工作日
                        const currentDate = new Date();
                        const currentYear = currentDate.getFullYear();
                        const currentMonth = currentDate.getMonth() + 1;

                        // 简单计算当月工作日（不考虑节假日）
                        const startDate = new Date(currentYear, currentMonth - 1, 1);
                        const endDate = new Date(currentYear, currentMonth, 0);
                        let workDays = 0;

                        for (let d = new Date(startDate); d <= endDate; d.setDate(d.getDate() + 1)) {
                            const dayOfWeek = d.getDay();
                            if (dayOfWeek !== 0 && dayOfWeek !== 6) {
                                workDays++;
                            }
                        }

                        console.log(`高管${initialValues.name}的实际出勤天数设置为计算的工作日: ${workDays}天`);
                        return workDays;
                    }

                    // 非高管员工的考勤处理
                    // 优先使用已保存的实际出勤天数（如果存在）
                    if (initialValues.actualAttendance !== undefined && initialValues.actualAttendance !== null) {
                        console.log(`使用已保存的实际出勤天数: ${initialValues.actualAttendance}天`);
                        return initialValues.actualAttendance;
                    }

                    // 如果有考勤数据，使用考勤数据
                    if (attendanceData) {
                        console.log(`使用考勤数据: ${attendanceData.actualAttendDays}天`);
                        return attendanceData.actualAttendDays;
                    }

                    // 如果没有任何数据，默认为0
                    console.log('没有出勤数据，默认为0天');
                    return 0;
                })(),
                // 使用初始值中的系数和调整值，不重新计算
                educationCoefficient: initialValues.educationCoefficient || 1.0,
                educationAdjustment: initialValues.educationAdjustment || 0,
                languageCoefficient: initialValues.languageCoefficient || 1.0,
                languageAdjustment: initialValues.languageAdjustment || 0,
                adjustedBaseSalary: initialValues.adjustedBaseSalary || 0
            };

            console.log('设置表单值:', formValues);
            console.log('岗位等级值:', formValues.positionLevel);
            
            // 设置表单值
            form.setFieldsValue(formValues);

            // 如果有计算结果，设置薪资结果
            if (initialValues.calculationResult) {
                setSalaryResult({
                    ...initialValues,
                    calculationResult: initialValues.calculationResult
                });
            }

            // 设置试用期状态 - 使用多种方式判断
            const probationStatus = isProbationPeriod(initialValues);
            setIsProbation(probationStatus);
        };
        
        initializeForm();
    }, [config, initialValues, form, positionType, defaultPositionLevel]);

    const determineLanguageLevel = (listening, speaking, reading, writing) => {
        const levels = [listening, speaking, reading, writing];
        const levelCounts = {
            '无': 0,
            '基础': 0,
            '熟练': 0,
            '精通': 0
        };

        levels.forEach(level => {
            if (levelCounts.hasOwnProperty(level)) {
                levelCounts[level]++;
            }
        });

        if (levelCounts['精通'] >= 2) {
            return '精通';
        } else if (levelCounts['熟练'] >= 2) {
            return '熟练';
        } else if (levelCounts['基础'] >= 2) {
            return '基础';
        } else {
            return '无';
        }
    };

    const getLanguageLevel = (initialValues) => {
        if (initialValues.languageLevel) {
            return initialValues.languageLevel;
        }

        return determineLanguageLevel(
            initialValues.languageListening || '无',
            initialValues.languageSpeaking || '无',
            initialValues.languageReading || '无',
            initialValues.languageWriting || '无'
        );
    };

    // 根据绩效等级获取对应的系数
    const getPerformanceCoefficient = (performanceLevel) => {
        return utilGetPerformanceCoefficient(performanceLevel, config);
    };

    // 根据绩效等级获取对应的名称
    const getPerformanceLevelName = (level) => {
        return utilGetPerformanceLevelName(level);
    };

    // 检查员工是否在试用期内
    const checkProbationStatus = (employeeData) => {
        return isProbationPeriod(employeeData);
    };

    // 获取员工考勤数据
    const fetchAttendanceData = async (employeeId, name) => {
        if (!employeeId && !name) {
            return null;
        }


        try {
            const currentDate = new Date();
            const year = initialValues.year || currentDate.getFullYear();
            const month = initialValues.month || (currentDate.getMonth() + 1);
            console.log('Using year and month for attendance fetch:', { year, month });

            const params = new URLSearchParams();
            if (employeeId) params.append('employeeId', employeeId);
            if (name) params.append('name', name);
            params.append('year', year.toString());
            params.append('month', month.toString());

            const url = `/api/attendance/employee-attendance?${params.toString()}`;
            console.log('Fetching attendance data from:', url);

            const response = await fetch(url, {
                headers: {
                    'Authorization': `Bearer ${localStorage.getItem('token') || ''}`,
                    'Content-Type': 'application/json'
                }
            });

            const text = await response.text();
            console.log('Response status:', response.status, 'OK:', response.ok);
            console.log('Raw response text:', text);

            if (!response.ok) {
                console.error('考勤接口异常返回:', text);
                return null;
            }

            const result = JSON.parse(text);
            if (result.success) {
                console.log('获取到员工考勤数据:', result.data);
                return result.data;
            }
            
            console.warn('获取考勤数据失败');
            return null;
        } catch (error) {
            console.error('获取考勤数据异常:', error);
            return null;
        }
    };

    // 学历映射函数
    const getHighestEducation = (initialValues) => {
        const {
            firstEducation, firstSchool, firstEducationType, firstSchoolType,
            finalEducation, finalSchool, finalEducationType, finalSchoolType
        } = initialValues;

        let highestEducation = '';

        // 检查是否为全日制学历
        const isFinalFullTime = finalEducationType === '全日制';
        const isFirstFullTime = firstEducationType === '全日制';

        console.log('学历类型检查:', {
            finalEducation, finalEducationType, finalSchoolType, isFinalFullTime,
            firstEducation, firstEducationType, firstSchoolType, isFirstFullTime,
            rawData: initialValues
        });

        // 如果两个学历都不是全日制，则视为大专及以下
        if (!isFinalFullTime && !isFirstFullTime) {
            highestEducation = '大专及以下';
            console.log('无有效全日制学历，视为大专及以下');
            return { level: highestEducation };
        }

        // 优先使用全日制的最高学历
        if (finalEducation && finalSchool && isFinalFullTime) {
            const schoolType = finalSchoolType === '985/211' ? '985/211 院校' : '普通院校';
            highestEducation = `${finalEducation}（${schoolType}）`;
            console.log('使用全日制最高学历:', finalEducation, finalSchoolType);
        }
        // 如果最高学历不是全日制，但第一学历是全日制，则使用第一学历
        else if (firstEducation && firstSchool && isFirstFullTime) {
            const schoolType = firstSchoolType === '985/211' ? '985/211 院校' : '普通院校';
            highestEducation = `${firstEducation}（${schoolType}）`;
            console.log('使用全日制第一学历:', firstEducation, firstSchoolType);
        }

        console.log('识别的最高学历:', highestEducation);
        return { level: highestEducation };
    };

    const handleFinish = async () => {
        setLoading(true);
        setError(null);
        try {
            const formData = form.getFieldsValue(true);

            // 计算学历和语言调整系数及调整值
            const education = formData.education || initialValues.education;
            const educationType = formData.educationType || initialValues.educationType || '全日制';
            const languageLevel = formData.languageLevel || initialValues.languageLevel;
            
            // 使用工具函数计算系数和调整值，传递学历类型
            // 非全日制学历按照大专及以下计算
            const mappedEducation = educationType === '非全日制' ? 
                '大专及以下' : 
                mapEducation(education, config, educationType);
            
            const educationCoefficient = getEducationCoefficient(mappedEducation, config);
            const educationAdjustment = calculateEducationAdjustment(mappedEducation, config, config?.SALARY_CONFIG?.BASE_SALARY || 3500);
            const languageCoefficient = getLanguageCoefficient(languageLevel, config);
            const languageAdjustment = calculateLanguageAdjustment(languageLevel, config, config?.SALARY_CONFIG?.BASE_SALARY || 3500);
            
            console.log('学历计算:', {
                原始学历: education,
                学历类型: educationType,
                映射学历: mappedEducation,
                系数: educationCoefficient,
                调整值: educationAdjustment
            });

            // 根据绩效等级获取对应的系数
            let performanceCoeff = 1.0;
            if (formData.performanceLevel && config?.SALARY_CONFIG?.PERFORMANCE_LEVELS) {
                performanceCoeff = config.SALARY_CONFIG.PERFORMANCE_LEVELS[formData.performanceLevel] || 1.0;
            }

            // 对于高管，在计算前自动同步实际出勤天数为额定出勤天数
            let finalActualAttendance = formData.actualAttendance;
            if (positionType === '高管') {
                // 获取额定出勤天数，优先使用表单中的requiredAttendance
                let requiredAttendance = form.getFieldValue('requiredAttendance');

                if (!requiredAttendance || requiredAttendance <= 0) {
                    // 如果没有额定出勤天数，计算当前月份的工作日
                    const currentDate = new Date();
                    const currentYear = currentDate.getFullYear();
                    const currentMonth = currentDate.getMonth() + 1;

                    // 简单计算当月工作日（不考虑节假日）
                    const startDate = new Date(currentYear, currentMonth - 1, 1);
                    const endDate = new Date(currentYear, currentMonth, 0);
                    let workDays = 0;

                    for (let d = new Date(startDate); d <= endDate; d.setDate(d.getDate() + 1)) {
                        const dayOfWeek = d.getDay();
                        if (dayOfWeek !== 0 && dayOfWeek !== 6) {
                            workDays++;
                        }
                    }
                    requiredAttendance = workDays;
                }

                // 同时更新表单中的实际出勤天数
                form.setFieldValue('actualAttendance', requiredAttendance);
                finalActualAttendance = requiredAttendance;

                console.log(`高管${formData.name}的实际出勤天数在计算时自动同步为: ${requiredAttendance}天`);
                message.info(`高管不参与考勤，实际出勤天数已自动设置为额定出勤天数: ${requiredAttendance}天`);
            }

            const updatedData = {
                employeeId: formData.employeeId,
                name: formData.name,
                department: formData.department,
                subDepartment: formData.subDepartment,
                positionType: formData.positionType,
                positionLevel: formData.positionLevel,
                education: mappedEducation, // 使用映射后的学历
                educationType: educationType, // 添加学历类型
                originalEducation: education, // 保存原始学历
                languageLevel: languageLevel,
                administrativeLevel: formData.administrativeLevel || '无',
                actualAttendance: Math.max(0, Number(finalActualAttendance) || 0),
                requiredAttendance: (() => {
                    // 获取额定出勤天数
                    const formRequiredAttendance = form.getFieldValue('requiredAttendance');
                    if (formRequiredAttendance && formRequiredAttendance > 0) {
                        return formRequiredAttendance;
                    }

                    // 如果表单中没有，计算当前月份的工作日作为备选
                    const currentDate = new Date();
                    const currentYear = currentDate.getFullYear();
                    const currentMonth = currentDate.getMonth() + 1;

                    const startDate = new Date(currentYear, currentMonth - 1, 1);
                    const endDate = new Date(currentYear, currentMonth, 0);
                    let workDays = 0;

                    for (let d = new Date(startDate); d <= endDate; d.setDate(d.getDate() + 1)) {
                        const dayOfWeek = d.getDay();
                        if (dayOfWeek !== 0 && dayOfWeek !== 6) {
                            workDays++;
                        }
                    }

                    return workDays;
                })(),
                performanceLevel: formData.performanceLevel || 'B',
                performanceCoefficient: performanceCoeff,
                isProbation: isProbation,
                workType: formData.workType || '全职',
                probationEndDate: formData.probationEndDate || '',
                probationFactor: isProbation ? 0.8 : 1.0,
                // 使用计算出的系数和调整值
                educationCoefficient: educationCoefficient,
                educationAdjustment: educationAdjustment,
                languageCoefficient: languageCoefficient,
                languageAdjustment: languageAdjustment,
                specialAllowance: {
                    remark: formData.specialAllowance?.remark || '',
                    amount: Math.max(0, Number(formData.specialAllowance?.amount || 0))
                },
                specialDeduction: {
                    amount: Math.max(0, Number(formData.specialDeduction?.amount || 0))
                }
            };

            console.log('发送给后端的计算数据:', {
                education: updatedData.education,
                languageLevel: updatedData.languageLevel,
                educationCoefficient: updatedData.educationCoefficient,
                educationAdjustment: updatedData.educationAdjustment,
                languageCoefficient: updatedData.languageCoefficient,
                languageAdjustment: updatedData.languageAdjustment,
                isProbation: updatedData.isProbation,
                performanceLevel: updatedData.performanceLevel,
                performanceCoefficient: updatedData.performanceCoefficient,
                actualAttendance: updatedData.actualAttendance,
                requiredAttendance: updatedData.requiredAttendance
            });

            try {
                const response = await fetch(
                    `${config?.apiBaseUrl || '/api'}/salary/calculate`,
                    {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'Authorization': `Bearer ${localStorage.getItem('token') || ''}`
                        },
                        body: JSON.stringify(updatedData)
                    }
                );

                if (!response.ok) {
                    const errorText = await response.text();
                    throw new Error(`服务器响应错误: ${response.status} - ${errorText}`);
                }

                const responseData = await response.json();

                if (responseData && responseData.payslip) {
                    // 获取当前表单值
                    const formValues = form.getFieldsValue();

                    // 使用计算出的系数和调整值，而不是后端返回的值
                    const result = {
                        ...responseData.payslip,
                        // 基础薪资相关字段
                        adjustedBaseSalary: responseData.payslip.adjustedBaseSalary !== undefined && responseData.payslip.adjustedBaseSalary !== null
                            ? Number(responseData.payslip.adjustedBaseSalary)
                            : 0,
                        // 试用期相关字段
                        isProbation: responseData.payslip.isProbation || false,
                        probationFactor: responseData.payslip.probationFactor || 1.0,
                        originalBaseSalary: responseData.payslip.originalBaseSalary !== undefined && responseData.payslip.originalBaseSalary !== null
                            ? Number(responseData.payslip.originalBaseSalary)
                            : 0,
                        originalPositionSalary: responseData.payslip.originalPositionSalary !== undefined && responseData.payslip.originalPositionSalary !== null
                            ? Number(responseData.payslip.originalPositionSalary)
                            : 0,
                        originalAdminSalary: responseData.payslip.originalAdminSalary !== undefined && responseData.payslip.originalAdminSalary !== null
                            ? Number(responseData.payslip.originalAdminSalary)
                            : 0,
                        originalPerformanceBonus: responseData.payslip.originalPerformanceBonus !== undefined && responseData.payslip.originalPerformanceBonus !== null
                            ? Number(responseData.payslip.originalPerformanceBonus)
                            : 0,
                        // 使用计算出的系数和调整值
                        educationAdjustment: educationAdjustment,
                        educationCoefficient: educationCoefficient,
                        languageAdjustment: languageAdjustment,
                        languageCoefficient: languageCoefficient,
                        // 添加学历和语言信息
                        education: education,
                        languageLevel: languageLevel,
                        // 其他字段保持不变
                        positionSalary: responseData.payslip.positionSalary !== undefined && responseData.payslip.positionSalary !== null
                            ? Number(responseData.payslip.positionSalary)
                            : 0,
                        adminSalary: responseData.payslip.adminSalary !== undefined && responseData.payslip.adminSalary !== null
                            ? Number(responseData.payslip.adminSalary)
                            : 0,
                        performanceBonus: responseData.payslip.performanceBonus !== undefined && responseData.payslip.performanceBonus !== null
                            ? Number(responseData.payslip.performanceBonus)
                            : 0,
                        // 其他计算结果
                        calculationResult: {
                            mealAllowance: responseData.payslip.calculationResult?.mealAllowance || 0,
                            communicationAllowance: responseData.payslip.calculationResult?.communicationAllowance || 0,
                            totalMonthlySalary: responseData.payslip.calculationResult?.totalMonthlySalary || 0,
                            socialInsurance: responseData.payslip.calculationResult?.socialInsurance || 0,
                            tax: responseData.payslip.calculationResult?.tax || 0,
                            netSalary: responseData.payslip.calculationResult?.netSalary || 0,
                            absenceDeduction: responseData.payslip.calculationResult?.absenceDeduction || 0,
                            attendanceAdjustment: responseData.payslip.calculationResult?.attendanceAdjustment || 0,
                            isProbation: isProbation,
                            probationFactor: isProbation ? 0.8 : 1.0
                        }
                    };
                    setSalaryResult(result);

                    // 更新试用期状态为后端返回的状态
                    setIsProbation(responseData.payslip.isProbation || false);

                    message.success('薪资计算成功');
                } else {
                    throw new Error(responseData?.message || '计算结果格式错误');
                }
            } catch (fetchError) {
                console.error('计算薪资失败:', fetchError);
                throw fetchError;
            }
        } catch (error) {
            setError(error.message);
            message.error('计算失败: ' + error.message);
        } finally {
            setLoading(false);
        }
    };

    const handleReset = async () => {
        try {
            if (!window.confirm('确定要重置该员工的薪资为0并保存吗？此操作不可撤销。')) {
                return;
            }

            setLoading(true);

            // 获取当前表单值中的员工基本信息
            const formValues = form.getFieldsValue();
            
            // 判断岗位类型
            const adminLevels = ['总经理', '副总经理', '总监/总工', '副总监/副总工'];
            const positionType = adminLevels.includes(initialValues.administrativeLevel) ? '高管' : 
                (initialValues.department?.includes('工程部') ? '技术' : '其他');
            
            const employeeInfo = {
                employeeId: formValues.employeeId,
                name: formValues.name,
                department: formValues.department,
                subDepartment: formValues.subDepartment,
                education: formValues.education || initialValues.education,
                languageLevel: formValues.languageLevel || initialValues.languageLevel,
                administrativeLevel: formValues.administrativeLevel || initialValues.administrativeLevel,
                positionType: positionType,
                isProbation: initialValues.isProbation || false,
                workType: initialValues.workType || '全职',
                probationEndDate: initialValues.probationEndDate || ''
            };
            
            // 使用默认薪资数据
            const resetData = {
                ...getDefaultSalaryData(employeeInfo),
                year: initialValues.year,
                month: initialValues.month
            };
            
            // 重置表单
            form.setFieldsValue(resetData);
            
            // 更新薪资结果状态
            setSalaryResult(resetData);

            console.log('重置薪资数据:', resetData);

            const savedData = await SalaryService.saveSalary(resetData);
            console.log('薪资已重置为0并保存:', savedData);

            message.success('薪资已重置为0并保存');

            // 通知父组件数据已更新，但不关闭窗口
            if (onOk) {
                onOk(savedData);
            }
        } catch (error) {
            console.error('重置薪资失败:', error);
            message.error('重置薪资失败: ' + error.message);
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        // 只在表单可见时设置事件处理程序
        if (visible) {
            const fields = ['positionLevel', 'administrativeLevel', 'performanceCoefficient'];

            // 设置事件处理程序
            form.setFields(fields.map(field => ({
                name: field,
                onSelect: () => setSalaryResult(null),
                onChange: () => setSalaryResult(null),
            })));

            // 清理函数
            return () => {
                // 确保在组件卸载或依赖项变化时清理事件处理程序
                form.setFields(fields.map(field => ({
                    name: field,
                    onSelect: undefined,
                    onChange: undefined,
                })));
            };
        }
    }, [form, visible]);

    return (
        <Modal
            title={
                <div
                    style={{
                        cursor: 'move',
                        textAlign: 'center',
                        width: '100%',
                        paddingTop: '24px'
                    }}
                    onMouseOver={() => setDisabled(false)}
                    onMouseOut={() => setDisabled(true)}
                >
                    编辑员工薪资信息
                </div>
            }
            className="salary-form-modal"
            width={900}
            style={{ minWidth: '880px', width: '880px' }}
            styles={{
                body: {
                    padding: '20px',
                    margin: '0 10px 0 10px',
                    minWidth: '860px',
                    width: '860px'
                },
                content: {
                    minWidth: '880px',
                    width: '880px'
                }
            }}
            open={visible}
            onCancel={() => {
                // 清理所有状态
                setSalaryResult(null);
                setError(null);
                form.resetFields();

                // 调用父组件的取消回调
                onCancel();
            }}
            modalRender={modal => (
                <Draggable
                    handle=".ant-modal-header"
                    nodeRef={dragRef}
                    disabled={disabled}
                >
                    <div ref={dragRef}>{modal}</div>
                </Draggable>
            )}
            footer={
                <div style={{ display: 'flex', justifyContent: 'center' }}>
                    <Space size="middle">
                        <Button
                            onClick={handleReset}
                            className="custom-button"
                            style={{ backgroundColor: '#f59e0b', borderColor: '#f59e0b', color: 'white' }}
                        >重置</Button>
                        <Button
                            type="primary"
                            onClick={() => {
                                form.validateFields()
                                    .then(() => handleFinish())
                                    .catch(() => {});
                            }}
                            loading={loading}
                            disabled={!!error}
                            className="custom-button"
                            style={{ backgroundColor: '#1890ff', borderColor: '#1890ff', color: 'white' }}
                        >
                            计算薪资
                        </Button>
                        {salaryResult && (
                            <Button
                                type="primary"
                                className="custom-button"
                                style={{ backgroundColor: '#10b981', borderColor: '#10b981', color: 'white' }}
                                onClick={async () => {
                                    try {
                                        const formValues = form.getFieldsValue();
                                        // 添加调试日志
                                        console.log('保存前的表单值:', formValues);
                                        console.log('保存前的初始值:', initialValues);

                                        // 在保存前验证数据一致性
                                        const validateData = () => {
                                            // 如果是重置的薪资（基本工资为0），不进行验证和修正
                                            if (Number(salaryResult.adjustedBaseSalary) === 0) {
                                                console.log('薪资已重置，跳过验证');
                                                return true;
                                            }
                                            
                                            const baseSalary = Number(config?.SALARY_CONFIG?.BASE_SALARY) || 3500;
                                            // 正确处理调整值，避免负值被错误处理为0
                                            const educationAdjustment = salaryResult.educationAdjustment !== undefined && salaryResult.educationAdjustment !== null
                                                ? Number(salaryResult.educationAdjustment) 
                                                : 0;
                                            const languageAdjustment = salaryResult.languageAdjustment !== undefined && salaryResult.languageAdjustment !== null
                                                ? Number(salaryResult.languageAdjustment) 
                                                : 0;

                                            // 计算预期的基本工资（试用期员工也显示原始值）
                                            let expectedBaseSalary = baseSalary + educationAdjustment + languageAdjustment;

                                            // 四舍五入到两位小数
                                            expectedBaseSalary = Math.round((expectedBaseSalary + Number.EPSILON) * 100) / 100;

                                            // 获取实际的基本工资
                                            const actualBaseSalary = Number(salaryResult.adjustedBaseSalary);

                                            // 计算差异（与预期基本工资比较）
                                            const difference = Math.abs(expectedBaseSalary - actualBaseSalary);

                                            // 如果差异大于0.01，则显示警告并修复数据
                                            if (difference >= 0.01) {
                                                console.warn('警告：基本工资数据不一致！', {
                                                    '基本工资': baseSalary,
                                                    '学历调整值': educationAdjustment,
                                                    '语言调整值': languageAdjustment,
                                                    '预期的基本工资': expectedBaseSalary,
                                                    '实际的基本工资': actualBaseSalary,
                                                    '差异': difference
                                                });

                                                // 修复数据 - 试用期员工也显示原始基本工资
                                                salaryResult.adjustedBaseSalary = expectedBaseSalary;
                                                salaryResult.originalBaseSalary = expectedBaseSalary;
                                            }

                                            // 确保系数字段存在
                                            if (salaryResult.educationCoefficient === undefined) {
                                                console.warn('警告: 缺少字段 educationCoefficient，使用默认值1.0');
                                                salaryResult.educationCoefficient = 1.0;
                                            }

                                            if (salaryResult.languageCoefficient === undefined) {
                                                console.warn('警告: 缺少字段 languageCoefficient，使用默认值1.0');
                                                salaryResult.languageCoefficient = 1.0;
                                            }

                                            return true;
                                        };

                                        // 验证数据一致性
                                        validateData();

                                        // 创建一个新的对象，明确不包含部门信息
                                        const dataToSave = {
                                            // 基本信息
                                            employeeId: initialValues.employeeId,
                                            name: initialValues.name,
                                            // 明确不包含部门信息，避免覆盖
                                            // department: initialValues.department,
                                            // subDepartment: initialValues.subDepartment,

                                            // 添加年月信息
                                            year: initialValues.year,
                                            month: initialValues.month,

                                            // 岗位信息
                                            positionType: formValues.positionType,
                                            positionLevel: formValues.positionLevel,
                                            education: formValues.education,
                                            educationType: formValues.educationType || '全日制', // 添加学历类型
                                            originalEducation: formValues.originalEducation, // 保存原始学历
                                            languageLevel: formValues.languageLevel,  // 添加语言等级
                                            administrativeLevel: initialValues.administrativeLevel,

                                            // 试用期信息
                                            isProbation: isProbation,
                                            workType: initialValues.workType || '全职',
                                            probationEndDate: initialValues.probationEndDate || '',
                                            probationFactor: isProbation ? 0.8 : 1.0, // 添加试用期系数

                                            // 薪资相关信息
                                            adjustedBaseSalary: salaryResult.adjustedBaseSalary,
                                            originalBaseSalary: salaryResult.originalBaseSalary || 0,  // 添加原始基本工资
                                            educationAdjustment: salaryResult.educationAdjustment,
                                            educationCoefficient: salaryResult.educationCoefficient || 1.0,
                                            languageAdjustment: salaryResult.languageAdjustment !== undefined ?
                                                salaryResult.languageAdjustment : 0,
                                            languageCoefficient: salaryResult.languageCoefficient || 1.0,
                                            positionSalary: salaryResult.positionSalary,
                                            originalPositionSalary: salaryResult.originalPositionSalary || 0,  // 添加原始岗位工资
                                            adminSalary: salaryResult.adminSalary,
                                            originalAdminSalary: salaryResult.originalAdminSalary || 0,  // 添加原始管理津贴
                                            performanceBonus: salaryResult.performanceBonus,
                                            originalPerformanceBonus: salaryResult.originalPerformanceBonus || 0,  // 添加原始绩效奖金
                                            performanceLevel: formValues.performanceLevel, // 添加绩效等级
                                            performanceCoefficient: formValues.performanceCoefficient,
                                            actualAttendance: formValues.actualAttendance,
                                            specialAllowance: formValues.specialAllowance,
                                            specialDeduction: formValues.specialDeduction,

                                            // 计算结果
                                            calculationResult: {
                                                mealAllowance: salaryResult.calculationResult.mealAllowance,
                                                communicationAllowance: salaryResult.calculationResult.communicationAllowance,
                                                totalMonthlySalary: salaryResult.calculationResult.totalMonthlySalary,
                                                socialInsurance: salaryResult.calculationResult.socialInsurance,
                                                tax: salaryResult.calculationResult.tax,
                                                netSalary: salaryResult.calculationResult.netSalary,
                                                absenceDeduction: salaryResult.calculationResult.absenceDeduction || 0,
                                                attendanceAdjustment: salaryResult.calculationResult.attendanceAdjustment || 0,
                                                isProbation: isProbation, // 添加试用期状态
                                                probationFactor: isProbation ? 0.8 : 1.0, // 添加试用期系数
                                                originalTotalSalary: isProbation ? (salaryResult.calculationResult.totalMonthlySalary / 0.8) : undefined, // 添加原始总薪资
                                            }
                                        };

                                        const savedData = await SalaryService.saveSalary(dataToSave);

                                        // 清理所有状态
                                        setSalaryResult(null);
                                        setError(null);
                                        form.resetFields();

                                        // 调用父组件的回调
                                        if (onOk) {
                                            onOk(savedData);
                                        }

                                        // 关闭表单
                                        onCancel();
                                    } catch (error) {
                                        message.error('保存失败: ' + error.message);
                                    }
                                }}
                            >
                                保存并关闭
                            </Button>
                        )}
                    </Space>
                </div>
            }
        >
            <Form
                form={form}
                onFinish={() => handleFinish()}
                onValuesChange={(changedValues) => {
                    // 只有在修改特定字段时才清除计算结果
                    if (changedValues.positionLevel ||
                        changedValues.actualAttendance ||
                        changedValues.performanceLevel ||
                        changedValues.performanceCoefficient ||
                        changedValues.specialAllowance ||
                        changedValues.specialDeduction) {
                        setSalaryResult(null);
                    }
                }}
                size="small"
                style={{ marginTop: '-25px' }}
            >
                <div className="form-section" style={{ width: '760px', minWidth: '760px', marginTop: '5px', marginBottom: '-5px' }}>
                    <h3 style={{ fontSize: '14px', margin: '2px 0' }}>基本信息</h3>
                    <div className="form-content" style={{ width: '100%' }}>
                        <div className="form-row" style={{ display: 'flex', gap: '5px', width: '100%', minWidth: '740px', marginBottom: '-20px' }}>
                            <div className="form-group" style={{ flex: '0.6' }}>
                                <label>工号</label>
                                <Form.Item name="employeeId" rules={[{ required: true }]} style={{ marginBottom: 0 }}>
                                    <Input disabled />
                                </Form.Item>
                            </div>
                            <div className="form-group" style={{ flex: '1.2' }}>
                                <label>姓名</label>
                                <Form.Item name="name" style={{ marginBottom: 0 }}>
                                    <Input disabled />
                                </Form.Item>
                            </div>
                            <div className="form-group" style={{ flex: '1.3' }}>
                                <label>所属部门</label>
                                <Input
                                    disabled
                                    value={form.getFieldValue('department') ?
                                        `${form.getFieldValue('department')}${form.getFieldValue('subDepartment') ? ` - ${form.getFieldValue('subDepartment')}` : ''}`
                                        : '-'}
                                    style={{ fontWeight: 'bold' }}
                                />
                                {/* 隐藏字段用 hidden input 代替 Form.Item，避免警告 */}
                                <input type="hidden" name="department" value={form.getFieldValue('department') || ''} />
                                <input type="hidden" name="subDepartment" value={form.getFieldValue('subDepartment') || ''} />
                            </div>
                            <div className="form-group" style={{ flex: '0.8' }}>
                                <label>岗位类型</label>
                                <Form.Item name="positionType" style={{ marginBottom: 0 }}>
                                    <Select disabled>
                                        <Select.Option value={form.getFieldValue('positionType')}>
                                            {form.getFieldValue('positionType')}
                                        </Select.Option>
                                    </Select>
                                </Form.Item>
                            </div>
                            <div className="form-group" style={{ flex: '0.8' }}>
                                <label>
                                    岗位等级
                                    <Popover
                                        content={<PositionLevelHelp />}
                                        title="岗位等级说明"
                                        trigger="click"
                                        placement="bottom"
                                        className="position-level-popover"
                                        styles={{
                                            body: { width: '280px', maxHeight: '500px', overflow: 'auto' }
                                        }}
                                    >
                                        <QuestionCircleOutlined
                                            style={{
                                                marginLeft: '5px',
                                                color: '#1890ff',
                                                cursor: 'pointer'
                                            }}
                                        />
                                    </Popover>
                                </label>
                                <Form.Item
                                    name="positionLevel"
                                    rules={[{ required: true, message: '请选择岗位等级' }]}
                                    style={{ marginBottom: 0 }}
                                    initialValue=""
                                >
                                    <Select>
                                        {(() => {
                                            // 直接根据岗位类型生成选项
                                            let levels = [];
                                            const currentPositionType = form.getFieldValue('positionType') || positionType;
                                            if (currentPositionType === '技术') {
                                                levels = Array.from({ length: 20 }, (_, i) => `A${i + 1}`);
                                            } else if (currentPositionType === '高管') {
                                                levels = Array.from({ length: 4 }, (_, i) => `B${i + 1}`);
                                            } else if (currentPositionType === '支持') {
                                                levels = Array.from({ length: 15 }, (_, i) => `C${i + 1}`);
                                            } else if (currentPositionType === '其他') {
                                                levels = Array.from({ length: 15 }, (_, i) => `D${i + 1}`);
                                            }
                                            // 只在 levels 非空时渲染 Option
                                            return Array.isArray(levels) && levels.length > 0
                                                ? levels.filter(level => level != null && level !== '').map(level => (
                                                    <Select.Option key={level} value={level}>
                                                        {level}
                                                    </Select.Option>
                                                ))
                                                : null;
                                        })()}
                                    </Select>
                                </Form.Item>
                            </div>
                            <div className="form-group" style={{ flex: '0.6' }}>
                                <label>实际出勤</label>
                                <Form.Item
                                    name="actualAttendance"
                                    initialValue={0}
                                    rules={[{ required: true, message: '请输入实际出勤天数' }]}
                                    style={{ marginBottom: 0 }}
                                >
                                    <Input
                                        type="number"
                                        min={0}
                                        max={31}
                                    />
                                </Form.Item>
                            </div>
                            <div className="form-group" style={{ flex: '0.85' }}>
                                <label>
                                    绩效等级
                                    <Popover
                                        content={<PerformanceLevelHelp />}
                                        title="绩效等级评定依据"
                                        trigger="click"
                                        placement="bottom"
                                        className="performance-level-popover"
                                        styles={{
                                            body: { width: '400px', maxHeight: '500px', overflow: 'auto' }
                                        }}
                                    >
                                        <QuestionCircleOutlined
                                            style={{
                                                marginLeft: '5px',
                                                color: '#1890ff',
                                                cursor: 'pointer'
                                            }}
                                        />
                                    </Popover>
                                </label>
                                <Form.Item
                                    name="performanceLevel"
                                    initialValue="B"
                                    rules={[{ required: true, message: '请选择绩效等级' }]}
                                    style={{ marginBottom: 0 }}
                                >
                                    <Select onChange={(value) => {
                                        // 当绩效等级变化时，自动设置对应的系数
                                        if (config?.SALARY_CONFIG?.PERFORMANCE_LEVELS) {
                                            const coeff = config.SALARY_CONFIG.PERFORMANCE_LEVELS[value] || 1.0;
                                            form.setFieldValue('performanceCoefficient', coeff);

                                            // 清除计算结果
                                            setSalaryResult(null);
                                        }
                                    }}>
                                        {Object.entries(config?.SALARY_CONFIG?.PERFORMANCE_LEVELS || {}).map(([level]) => (
                                            <Select.Option key={level} value={level}>
                                                {level} ({utilGetPerformanceLevelName(level)})
                                            </Select.Option>
                                        ))}
                                    </Select>
                                </Form.Item>
                                <Form.Item name="performanceCoefficient" style={{ display: 'none' }}>
                                    <Input type="hidden" />
                                </Form.Item>
                            </div>
                        </div>

                        {/* 高管不参与考勤提示 */}
                        {positionType === '高管' && (
                            <div style={{
                                marginTop: '8px',
                                marginBottom: '8px',
                                padding: '8px 12px',
                                backgroundColor: '#f0f8ff',
                                border: '1px solid #d1ecf1',
                                borderRadius: '4px',
                                fontSize: '13px',
                                color: '#0c5460',
                                textAlign: 'center'
                            }}>
                                <i className="info-icon" style={{ marginRight: '6px' }}>ℹ️</i>
                                高管不参与考勤计算，实际出勤天数已自动设置为当月工作日数量
                            </div>
                        )}

                        <div className="form-row" style={{ display: 'flex', gap: '5px', width: '100%', minWidth: '740px',  marginBottom: '-35px' }}>
                            <div className="form-group" style={{ flex: '1.2' }}>
                                <label>专项附加扣除</label>
                                <div style={{ marginBottom: 0, display: 'flex', alignItems: 'center' }}>
                                    <Form.Item
                                        name={['specialDeduction', 'amount']}
                                        style={{ display: 'inline-block', width: '100px', marginRight: '16px', marginBottom: 0 }}
                                        initialValue={0}
                                        rules={[
                                            {
                                                validator: (_, value) => {
                                                    if (value === null || value === undefined || value === '') {
                                                        return Promise.resolve();
                                                    }
                                                    const num = Number(value);
                                                    if (isNaN(num) || num < 0) {
                                                        return Promise.reject('请输入有效的正数');
                                                    }
                                                    return Promise.resolve();
                                                }
                                            }
                                        ]}
                                    >
                                        <InputNumber
                                            placeholder="0"
                                            style={{ width: '100%' }}
                                            min={0}
                                            precision={2}
                                            formatter={value => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                                            parser={value => value.replace(/¥\s?|(,*)/g, '')}
                                        />
                                    </Form.Item>
                                    <span style={{ color: '#666', fontSize: '12px'}}>
                                        （说明：此处金额为个人所得税法规定的专项附加扣除项的每个单项合计金额）
                                    </span>
                                </div>
                            </div>
                        </div>
                        <div className="form-row" style={{ display: 'flex', gap: '5px', width: '100%', minWidth: '740px', marginTop: '10px', marginBottom: '-15px' }}>
                            <div className="form-group" style={{ flex: '0.9' }}>
                                <label>学历</label>
                                <Form.Item name="education" style={{ marginBottom: 0 }} initialValue="">
                                    <Select disabled value={form.getFieldValue('education') || ''}>
                                        {form.getFieldValue('education') ? (
                                            <Select.Option value={form.getFieldValue('education')}>
                                                {form.getFieldValue('education')}
                                            </Select.Option>
                                        ) : null}
                                    </Select>
                                </Form.Item>
                            </div>
                            <div className="form-group" style={{ flex: '0.6' }}>
                                <label>外语等级</label>
                                <Form.Item name="languageLevel" style={{ marginBottom: 0 }} initialValue="">
                                    <Select disabled value={form.getFieldValue('languageLevel') || ''}>
                                        {form.getFieldValue('languageLevel') ? (
                                            <Select.Option value={form.getFieldValue('languageLevel')}>
                                                {form.getFieldValue('languageLevel')}
                                            </Select.Option>
                                        ) : null}
                                    </Select>
                                </Form.Item>
                            </div>
                            <div className="form-group" style={{ flex: '0.8' }}>
                                <label>管理岗位级别</label>
                                <Form.Item name="administrativeLevel" style={{ marginBottom: 0 }}>
                                    <Input disabled />
                                </Form.Item>
                            </div>
                            <div className="form-group" style={{ flex: '2' }}>
                                <label>特殊津贴</label>
                                <div style={{ marginBottom: 0, display: 'flex', alignItems: 'center' }}>
                                    <Form.Item
                                        name={['specialAllowance', 'remark']}
                                        style={{ display: 'inline-block', width: 'calc(50% - 8px)', marginRight: '16px', marginBottom: 0 }}
                                    >
                                        <Input placeholder="备注" />
                                    </Form.Item>
                                    <Form.Item
                                        name={['specialAllowance', 'amount']}
                                        style={{ display: 'inline-block', width: 'calc(50% - 8px)', marginBottom: 0 }}
                                        rules={[
                                            {
                                                validator: (_, value) => {
                                                    if (value && (isNaN(value) || value < 0)) {
                                                        return Promise.reject('请输入有效的金额');
                                                    }
                                                    return Promise.resolve();
                                                }
                                            }
                                        ]}
                                    >
                                        <InputNumber
                                            placeholder="0"
                                            style={{ width: '100%' }}
                                            min={0}
                                            precision={2}
                                            formatter={value => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                                            parser={value => value.replace(/¥\s?|(,*)/g, '')}
                                        />
                                    </Form.Item>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                {salaryResult && (
                    <div className="form-section" style={{ width: '760px', minWidth: '760px', marginBottom: '-30px' }}>
                        <h3 style={{ fontSize: '14px', margin: '2px 0' }}>薪资计算结果</h3>
                        <div className="form-content" style={{ padding: '12px 8px 8px', border: '1px solid #f0f0f0', width: '100%', minWidth: '740px' }}>
                            <Row gutter={16}>
                                <Col span={8}>
                                    <p>基本工资: {formatCurrency(safeNumber(salaryResult.adjustedBaseSalary))}</p>
                                    <p>学历调整: {formatCurrency(salaryResult.educationAdjustment !== undefined && salaryResult.educationAdjustment !== null
                                        ? Number(salaryResult.educationAdjustment) 
                                        : 0)}</p>
                                    <p>语言调整: {formatCurrency(salaryResult.languageAdjustment !== undefined && salaryResult.languageAdjustment !== null
                                        ? Number(salaryResult.languageAdjustment) 
                                        : 0)}</p>
                                    <p>岗位工资: {formatCurrency(safeNumber(salaryResult.positionSalary))}</p>
                                    <p>管理岗位工资: {formatCurrency(safeNumber(salaryResult.adminSalary))}</p>
                                </Col>
                                <Col span={8}>
                                    <p>绩效奖金: {formatCurrency(safeNumber(salaryResult.performanceBonus))}</p>
                                    <p>员工餐补: {formatCurrency(safeNumber(salaryResult.calculationResult?.mealAllowance))}</p>
                                    <p>通讯补贴: {formatCurrency(safeNumber(salaryResult.calculationResult?.communicationAllowance))}</p>
                                    <p>特殊津贴: {formatCurrency(safeNumber(salaryResult.specialAllowance?.amount))}</p>
                                    <p>专项附加扣除: {formatCurrency(safeNumber(salaryResult.specialDeduction?.amount))}</p>
                                </Col>
                                <Col span={8}>
                                    {(() => {
                                        const actualAttendance = form.getFieldValue('actualAttendance') || 0;
                                        const requiredAttendance = form.getFieldValue('requiredAttendance') || 22;

                                        if (actualAttendance < requiredAttendance) {
                                            return (
                                                <p style={{ color: 'red' }}>
                                                    缺勤扣除: {formatCurrency(-safeNumber(salaryResult.calculationResult?.absenceDeduction))}
                                                </p>
                                            );
                                        } else if (actualAttendance > requiredAttendance) {
                                            return (
                                                <p style={{ color: 'green' }}>
                                                    加班补助: {formatCurrency(safeNumber(salaryResult.calculationResult?.attendanceAdjustment))}
                                                </p>
                                            );
                                        } else {
                                            return (
                                                <p>
                                                    出勤调整: {formatCurrency(0)}
                                                </p>
                                            );
                                        }
                                    })()}
                                    <p>社保扣除: {formatCurrency(safeNumber(salaryResult.calculationResult.socialInsurance))}</p>
                                    <p>个税扣除: {formatCurrency(safeNumber(salaryResult.calculationResult.tax))}</p>
                                </Col>
                            </Row>
                            <Row gutter={16} style={{
                                borderTop: '1px dashed #f0f0f0',
                                marginTop: '8px',
                                paddingTop: '8px'
                            }}>
                                <Col span={14}>
                                    <div style={{
                                        fontSize: '11px',
                                        color: '#666',
                                        backgroundColor: '#f9f9f9',
                                        padding: '4px',
                                        borderRadius: '4px',
                                        lineHeight: '1.2'
                                    }}>
                                        <div style={{ marginBottom: '6px' }}>
                                            <span style={{ display: 'inline-block', width: '50%' }}>
                                                学历: {form.getFieldValue('education')}
                                                {form.getFieldValue('educationType') === '非全日制' && 
                                                 <span style={{ color: '#f5222d', fontSize: '10px', marginLeft: '4px' }}>(非全日制)</span>}
                                            </span>
                                            <span>外语水平: {form.getFieldValue('languageLevel')}</span>
                                        </div>
                                        <div style={{ marginBottom: '6px' }}>
                                            <span style={{ display: 'inline-block', width: '50%' }}>
                                                学历系数: {(() => {
                                                    // 检查薪资是否被重置（更严格的判断）
                                                    const isResetSalary = Number(salaryResult.adjustedBaseSalary) === 0;
                                                    if (isResetSalary) {
                                                        return '1.00';
                                                    }
                                                    return salaryResult.educationCoefficient ? salaryResult.educationCoefficient.toFixed(2) : '1.00';
                                                })()}
                                            </span>
                                            <span>
                                                语言系数: {(() => {
                                                    // 检查薪资是否被重置（更严格的判断）
                                                    const isResetSalary = Number(salaryResult.adjustedBaseSalary) === 0;
                                                    if (isResetSalary) {
                                                        return '1.00';
                                                    }
                                                    return salaryResult.languageCoefficient ? salaryResult.languageCoefficient.toFixed(2) : '1.00';
                                                })()}
                                            </span>
                                        </div>
                                        <div style={{ marginBottom: '6px' }}>
                                            <span style={{ display: 'inline-block', width: '100%' }}>
                                            基本工资: {(() => {
                                                // 检查薪资是否被重置（更严格的判断）
                                                const isResetSalary = Number(salaryResult.adjustedBaseSalary) === 0;
                                                if (isResetSalary) {
                                                    return '已重置为 0（系统将使用默认系数：学历1.00，语言1.00）';
                                                }
                                                
                                                const baseSalaryPart = formatCurrency(safeNumber(config?.SALARY_CONFIG?.BASE_SALARY, 3500)).replace('¥', '');
                                                const educationPart = Number(salaryResult.educationAdjustment) >= 0
                                                    ? ` + ${formatCurrency(safeNumber(salaryResult.educationAdjustment)).replace('¥', '')}`
                                                    : ` - ${formatCurrency(Math.abs(safeNumber(salaryResult.educationAdjustment))).replace('¥', '')}`;
                                                const languagePart = Number(salaryResult.languageAdjustment) >= 0
                                                    ? ` + ${formatCurrency(safeNumber(salaryResult.languageAdjustment)).replace('¥', '')}`
                                                    : ` - ${formatCurrency(Math.abs(safeNumber(salaryResult.languageAdjustment))).replace('¥', '')}`;
                                                const finalPart = ` = ${formatCurrency(safeNumber(salaryResult.adjustedBaseSalary)).replace('¥', '')}`;

                                                return baseSalaryPart + educationPart + languagePart + finalPart;
                                            })()}
                                            </span>
                                        </div>
                                        <div style={{ marginBottom: '6px' }}>
                                            <span style={{ display: 'inline-block', width: '50%' }}>
                                                出勤天数: {form.getFieldValue('actualAttendance')} / {form.getFieldValue('requiredAttendance') || 22}
                                            </span>
                                            <span>出勤比例: {((form.getFieldValue('actualAttendance') || 0) / (form.getFieldValue('requiredAttendance') || 22)).toFixed(2)}</span>
                                        </div>
                                        <div style={{ marginBottom: '6px' }}>
                                            <span style={{ display: 'inline-block', width: '50%' }}>
                                            绩效等级: {form.getFieldValue('performanceLevel')} ({utilGetPerformanceLevelName(form.getFieldValue('performanceLevel'))})，
                                            系数: {(() => {
                                                // 检查薪资是否被重置（更严格的判断）
                                                const isResetSalary = Number(salaryResult.adjustedBaseSalary) === 0;
                                                if (isResetSalary) {
                                                    return '1.00';
                                                }
                                                return form.getFieldValue('performanceCoefficient') ? Number(form.getFieldValue('performanceCoefficient')).toFixed(2) : '1.00';
                                            })()}
                                            </span>
                                            <span>
                                            社保缴费基数: {(() => {
                                                // 检查薪资是否被重置（更严格的判断）
                                                const isResetSalary = Number(salaryResult.adjustedBaseSalary) === 0;
                                                if (isResetSalary) {
                                                    return '¥0';
                                                }

                                                // 根据配置确定显示的社保基数
                                                if (config?.SALARY_CONFIG?.INSURANCE_BASE?.type === 'fixed') {
                                                    return `¥${config.SALARY_CONFIG.INSURANCE_BASE.fixedAmount || 5000}`;
                                                } else {
                                                    return `¥${Math.floor(salaryResult.adjustedBaseSalary * 100) / 100}`;
                                                }
                                            })()}
                                            </span>
                                        </div>
                                    </div>
                                </Col>
                                <Col span={10} style={{ textAlign: 'right' }}>
                                    <h4>
                                        应发工资: {formatCurrency(Number(salaryResult.calculationResult.totalMonthlySalary))}
                                    </h4>
                                    <h3>税后实发: {formatCurrency(Number(salaryResult.calculationResult.netSalary))}</h3>
                                    {isProbation && (
                                        <div style={{
                                            marginTop: '10px',
                                            padding: '8px 12px',
                                            backgroundColor: '#e6f7ff',
                                            border: '1px solid #91d5ff',
                                            borderRadius: '4px',
                                            fontSize: '12px',
                                            color: '#1890ff',
                                            textAlign: 'left'
                                        }}>
                                            <span style={{ fontWeight: 'bold' }}>薪资说明：</span> 该员工处于试用期，系统计算其税前应发工资为正常工资的80%。
                                        </div>
                                    )}
                                </Col>
                            </Row>
                        </div>
                    </div>
                )}
            </Form>
        </Modal>
    );
};

export default SalaryForm;
