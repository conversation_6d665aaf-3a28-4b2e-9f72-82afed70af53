import React, { useState, useEffect, useCallback } from 'react';
import { useNavigate, Link } from 'react-router-dom';
import { message, Modal } from '../utils/warningUtils';
import { UserProfile } from './UserProfile';
import {
    HomeOutlined,
    TeamOutlined,
    DollarOutlined,
    ScheduleOutlined,
    SettingOutlined,
    ToolOutlined,
    LogoutOutlined,
    ClockCircleOutlined
} from '@ant-design/icons';
import './Layout.css';
import eventBus, { USER_UPDATED } from '../utils/eventBus';
import ProbationReminder from './probation/ProbationReminder';

const Layout = ({ children }) => {
    const navigate = useNavigate();
    const [user, setUser] = useState(null);
    const [showUserProfile, setShowUserProfile] = useState(false);
    const [sidebarCollapsed, setSidebarCollapsed] = useState(false);

    // 从本地存储中获取侧边栏状态
    useEffect(() => {
        // 强制重置侧边栏状态，确保可见
        localStorage.setItem('sidebarCollapsed', 'false');
        setSidebarCollapsed(false);
        console.log('强制重置侧边栏状态为可见');
    }, []);

    // 切换侧边栏状态
    const toggleSidebar = () => {
        const newState = !sidebarCollapsed;
        setSidebarCollapsed(newState);
        localStorage.setItem('sidebarCollapsed', newState.toString());
        console.log('Sidebar collapsed state changed to:', newState);
    };

    // 加载用户数据的函数
    const loadUserData = useCallback(() => {
        try {
            // 检查token
            const token = localStorage.getItem('token');
            if (!token) {
                console.warn('Layout: 未找到token，重定向到登录页面');
                navigate('/login');
                return;
            }

            // 尝试从本地存储获取用户数据
            const userData = localStorage.getItem('user');
            let parsedUser = null;

            if (userData) {
                try {
                    parsedUser = JSON.parse(userData);
                    console.log('布局组件中的用户数据:', parsedUser);
                    console.log('用户角色类型:', typeof parsedUser.role);
                    console.log('用户角色值:', parsedUser.role);
                } catch (parseError) {
                    console.error('Layout: 解析用户数据失败:', parseError);
                    parsedUser = null;
                }
            }

            // 如果没有有效的用户数据，尝试从token中提取基本信息
            if (!parsedUser) {
                try {
                    // 从token中提取基本信息（不验证签名）
                    const tokenParts = token.split('.');
                    if (tokenParts.length === 3) {
                        const tokenPayload = JSON.parse(atob(tokenParts[1]));
                        console.log('Layout: 从token中提取的用户信息:', tokenPayload);

                        parsedUser = {
                            userId: tokenPayload.userId,
                            username: tokenPayload.username,
                            role: tokenPayload.role || 'user',
                            phone: ''
                        };

                        // 保存到本地存储
                        localStorage.setItem('user', JSON.stringify(parsedUser));
                        console.log('Layout: 已从token中恢复用户信息并保存到本地存储');
                    }
                } catch (tokenError) {
                    console.error('Layout: 从token中提取用户信息失败:', tokenError);
                }
            }

            // 如果仍然没有有效的用户数据，创建一个临时用户
            if (!parsedUser) {
                parsedUser = {
                    userId: '',
                    username: '临时用户',
                    role: 'user',
                    phone: ''
                };
                console.warn('Layout: 创建临时用户数据');
            }

            setUser(parsedUser);
        } catch (error) {
            console.error('Layout: 加载用户数据时出错:', error);
            // 出错时重定向到登录页面
            navigate('/login');
        }
    }, [navigate]);

    // 初始加载用户数据
    useEffect(() => {
        loadUserData();
    }, [loadUserData]);

    // 监听用户更新事件
    useEffect(() => {
        // 订阅用户更新事件
        const unsubscribe = eventBus.subscribe(USER_UPDATED, (updatedUser) => {
            console.log('Layout组件收到用户更新事件:', updatedUser);
            setUser(updatedUser);
        });

        // 组件卸载时取消订阅
        return () => {
            unsubscribe();
        };
    }, []);

    const handleLogout = () => {
        Modal.confirm({
            title: '确认登出',
            content: '您确定要登出系统吗？',
            onOk: () => {
                localStorage.removeItem('token');
                localStorage.removeItem('user');
                message.success('已成功登出');
                navigate('/login');
            }
        });
    };

    const toggleUserProfile = () => {
        setShowUserProfile(!showUserProfile);
    };

    return (
        <div className="layout-container">
            <header className="layout-header">
                <div className="logo">
                    <span>君正工程ERP系统</span>
                </div>
                <div className="header-right">
                    {/* 试用期提醒 */}
                    <ProbationReminder />

                    {user && (
                        <div className="user-info" onClick={toggleUserProfile}>
                            <div className="user-avatar">
                                {user.username ? user.username.charAt(0).toUpperCase() : 'U'}
                            </div>
                            <div className="user-text">
                                <span className="username">{user.username}</span>
                                <span className="user-role">
                                    {user.role === 'admin' ? '管理员' : '普通用户'}
                                </span>
                            </div>
                        </div>
                    )}
                    <nav className="dashboard-nav">
                        <button onClick={() => navigate('/employeeList')}>员工管理</button>
                        <button onClick={() => navigate('/salary')}>薪资管理</button>
                        <button onClick={() => navigate('/attendance')}>考勤管理</button>
                    </nav>
                    <button className="logout-button" onClick={handleLogout}>
                        登出
                    </button>
                </div>
            </header>

            <div className="layout-content">
                <nav className={`sidebar ${sidebarCollapsed ? 'collapsed' : ''}`} id="main-sidebar">
                    <button
                        className={`sidebar-toggle ${sidebarCollapsed ? 'collapsed' : ''}`}
                        id="sidebar-toggle-button"
                        onClick={toggleSidebar}

                    >
                        {sidebarCollapsed ? '◀' : '▶'}
                    </button>
                    <ul>
                        <li>
                            <Link to="/dashboard" className={window.location.hash.includes('/dashboard') ? 'active' : ''}>
                                <HomeOutlined className="sidebar-icon" />
                                {!sidebarCollapsed && "首页"}
                            </Link>
                        </li>
                        <li>
                            <Link to="/employeeList" className={window.location.hash.includes('/employeeList') ? 'active' : ''}>
                                <TeamOutlined className="sidebar-icon" />
                                {!sidebarCollapsed && "员工管理"}
                            </Link>
                        </li>
                        <li>
                            <Link to="/salary" className={window.location.hash.includes('/salary') ? 'active' : ''}>
                                <DollarOutlined className="sidebar-icon" />
                                {!sidebarCollapsed && "薪资管理"}
                            </Link>
                        </li>
                        <li>
                            <Link to="/attendance" className={window.location.hash.includes('/attendance') ? 'active' : ''}>
                                <ScheduleOutlined className="sidebar-icon" />
                                {!sidebarCollapsed && "考勤管理"}
                            </Link>
                        </li>
                        <li>
                            <Link to="/probation-management" className={window.location.hash.includes('/probation-management') ? 'active' : ''}>
                                <ClockCircleOutlined className="sidebar-icon" />
                                {!sidebarCollapsed && "试用期管理"}
                            </Link>
                        </li>
                        <li>
                            <Link to="/user-settings" className={window.location.hash.includes('/user-settings') ? 'active' : ''}>
                                <SettingOutlined className="sidebar-icon" />
                                {!sidebarCollapsed && "用户设置"}
                            </Link>
                        </li>

                        {/* 管理员入口 - 只对管理员显示 */}
                        {user && user.role === 'admin' && (
                            <li>
                                <Link to="/admin" className={window.location.hash.includes('/admin') ? 'active' : ''}>
                                    <ToolOutlined className="sidebar-icon" />
                                    {!sidebarCollapsed && "管理员模块"}
                                </Link>
                            </li>
                        )}

                        <li className="sidebar-logout">
                            <a href="#" onClick={handleLogout}>
                                <LogoutOutlined className="sidebar-icon" />
                                {!sidebarCollapsed && "登出"}
                            </a>
                        </li>
                    </ul>
                </nav>

                <main className="main-content">
                    {children}
                </main>
            </div>

            {showUserProfile && (
                <UserProfile onClose={() => setShowUserProfile(false)} />
            )}
        </div>
    );
};

export default Layout;