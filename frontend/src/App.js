import React, { useEffect } from 'react';
import { HashRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import Login from './components/Login';
import AdminLogin from './components/AdminLogin';
import Register from './components/Register';
import ForgotPassword from './components/ForgotPassword';
import Dashboard from './components/Dashboard';
import SalaryList from './components/salary/SalaryList';

import EmployeeList from './components/employee/EmployeeList';
import EmployeeForm from './components/employee/EmployeeForm';
import EmployeeDetails from './components/employee/EmployeeDetails';
import ErrorPage from './components/ErrorPage';
import AttendanceManagement from './components/attendance/AttendanceManagement';
import UserSettings from './components/UserSettings';
import ProtectedRoute from './components/ProtectedRoute';
import DebugLocalStorage from './components/DebugLocalStorage';

// 导入管理员组件
import AdminLayout from './components/admin/AdminLayoutNew';
import SalaryConfig from './components/admin/SalaryConfig';
import UserManagement from './components/admin/UserManagement';
import DatabaseManagement from './components/admin/DatabaseManagement';
import AttendanceConfigManagement from './components/admin/AttendanceConfigManagement';
import ProbationManagement from './components/probation/ProbationManagement';

import config from './config';
import 'antd/dist/antd.css'; // For Ant Design 4.x

config.refresh();

function App() {
    useEffect(() => {
        // 应用启动时刷新配置
        console.log('应用启动，刷新配置');
        config.refresh();
    }, []);
  console.log('App 组件渲染');

  return (
    <Router>
      <Routes>
        <Route path="/login" element={<Login />} />
        <Route path="/admin-login" element={<AdminLogin />} />
        <Route path="/register" element={<Register />} />
        <Route path="/forgot-password" element={<ForgotPassword />} />
        <Route path="/" element={<Navigate to="/login" />} />
        <Route path="/dashboard" element={
            <ProtectedRoute><Dashboard /></ProtectedRoute>
        } />
        <Route path="/employeeList" element={
            <ProtectedRoute><EmployeeList /></ProtectedRoute>
        } />
        <Route path="/employeeList/add" element={
            <ProtectedRoute><EmployeeForm /></ProtectedRoute>
        } />
        <Route path="/employeeList/edit/:id" element={
            <ProtectedRoute><EmployeeForm /></ProtectedRoute>
        } />
        <Route path="/employeeList/details/:id" element={
            <ProtectedRoute><EmployeeDetails /></ProtectedRoute>
        } />
        <Route path="/salary" element={
            <ProtectedRoute><SalaryList /></ProtectedRoute>
        } />

        <Route path="/attendance" element={
            <ProtectedRoute><AttendanceManagement /></ProtectedRoute>
        } />
        <Route path="/probation-management" element={
            <ProtectedRoute><ProbationManagement /></ProtectedRoute>
        } />
        <Route path="/user-settings" element={
            <ProtectedRoute><UserSettings /></ProtectedRoute>
        } />

        {/* 调试路由 */}
        <Route path="/debug" element={<DebugLocalStorage />} />

        {/* 管理员路由 */}
        <Route path="/admin" element={<AdminLayout />}>
            <Route index element={<Navigate to="/admin/salary-config" />} />
            <Route path="salary-config" element={<SalaryConfig />} />
            <Route path="users" element={<UserManagement />} />
            <Route path="attendance-config" element={<AttendanceConfigManagement />} />
            <Route path="database" element={<DatabaseManagement />} />
        </Route>

        <Route path="*" element={<ErrorPage />} />
      </Routes>
    </Router>
  );
}

export default App;
