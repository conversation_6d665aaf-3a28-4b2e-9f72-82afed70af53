const { calculateMonthlySalary } = require('./backend/utils/SalaryCalculator');

// 测试用例：验证薪资计算的正确性
function testSalaryCalculation() {
    console.log('=== 薪资计算测试 ===\n');

    // 测试用例1：正常员工，满勤
    console.log('测试用例1：正常员工，满勤');
    const testCase1 = {
        positionLevel: 'A5',
        positionType: '技术',
        education: '本科（普通院校）',
        languageLevel: '无',
        administrativeLevel: '无',
        performanceCoefficient: 1.0,
        actualAttendance: 22,
        requiredAttendance: 22,
        specialAllowance: { amount: 0 },
        specialDeduction: { amount: 0 },
        isProbation: false,
        workType: '全职'
    };

    const result1 = calculateMonthlySalary(testCase1);
    console.log('计算结果：');
    console.log('- 基本工资:', result1.adjustedBaseSalary);
    console.log('- 岗位工资:', result1.positionSalary);
    console.log('- 餐补:', result1.calculationResult.mealAllowance);
    console.log('- 通讯补贴:', result1.calculationResult.communicationAllowance);
    console.log('- 缺勤扣除:', result1.calculationResult.absenceDeduction);
    console.log('- 加班补助:', result1.calculationResult.attendanceAdjustment);
    console.log('- 应发工资:', result1.calculationResult.totalMonthlySalary);
    console.log('- 实发工资:', result1.calculationResult.netSalary);
    
    // 验证各项之和
    const expectedTotal = result1.adjustedBaseSalary + result1.positionSalary + 
                         result1.calculationResult.mealAllowance + 
                         result1.calculationResult.communicationAllowance +
                         result1.calculationResult.attendanceAdjustment -
                         result1.calculationResult.absenceDeduction;
    console.log('- 各项之和:', expectedTotal);
    console.log('- 计算总额:', result1.calculationResult.totalMonthlySalary);
    console.log('- 差异:', Math.abs(expectedTotal - result1.calculationResult.totalMonthlySalary));
    console.log('');

    // 测试用例2：员工缺勤
    console.log('测试用例2：员工缺勤（实际出勤20天）');
    const testCase2 = {
        ...testCase1,
        actualAttendance: 20,
        requiredAttendance: 22
    };

    const result2 = calculateMonthlySalary(testCase2);
    console.log('计算结果：');
    console.log('- 基本工资:', result2.adjustedBaseSalary);
    console.log('- 岗位工资:', result2.positionSalary);
    console.log('- 餐补:', result2.calculationResult.mealAllowance);
    console.log('- 通讯补贴:', result2.calculationResult.communicationAllowance);
    console.log('- 缺勤扣除:', result2.calculationResult.absenceDeduction);
    console.log('- 加班补助:', result2.calculationResult.attendanceAdjustment);
    console.log('- 应发工资:', result2.calculationResult.totalMonthlySalary);
    console.log('- 实发工资:', result2.calculationResult.netSalary);
    
    // 验证各项之和
    const expectedTotal2 = result2.adjustedBaseSalary + result2.positionSalary + 
                          result2.calculationResult.mealAllowance + 
                          result2.calculationResult.communicationAllowance +
                          result2.calculationResult.attendanceAdjustment -
                          result2.calculationResult.absenceDeduction;
    console.log('- 各项之和:', expectedTotal2);
    console.log('- 计算总额:', result2.calculationResult.totalMonthlySalary);
    console.log('- 差异:', Math.abs(expectedTotal2 - result2.calculationResult.totalMonthlySalary));
    console.log('');

    // 测试用例3：员工加班
    console.log('测试用例3：员工加班（实际出勤24天）');
    const testCase3 = {
        ...testCase1,
        actualAttendance: 24,
        requiredAttendance: 22
    };

    const result3 = calculateMonthlySalary(testCase3);
    console.log('计算结果：');
    console.log('- 基本工资:', result3.adjustedBaseSalary);
    console.log('- 岗位工资:', result3.positionSalary);
    console.log('- 餐补:', result3.calculationResult.mealAllowance);
    console.log('- 通讯补贴:', result3.calculationResult.communicationAllowance);
    console.log('- 缺勤扣除:', result3.calculationResult.absenceDeduction);
    console.log('- 加班补助:', result3.calculationResult.attendanceAdjustment);
    console.log('- 应发工资:', result3.calculationResult.totalMonthlySalary);
    console.log('- 实发工资:', result3.calculationResult.netSalary);
    
    // 验证各项之和
    const expectedTotal3 = result3.adjustedBaseSalary + result3.positionSalary + 
                          result3.calculationResult.mealAllowance + 
                          result3.calculationResult.communicationAllowance +
                          result3.calculationResult.attendanceAdjustment -
                          result3.calculationResult.absenceDeduction;
    console.log('- 各项之和:', expectedTotal3);
    console.log('- 计算总额:', result3.calculationResult.totalMonthlySalary);
    console.log('- 差异:', Math.abs(expectedTotal3 - result3.calculationResult.totalMonthlySalary));
    console.log('');

    // 测试用例4：试用期员工
    console.log('测试用例4：试用期员工');
    const testCase4 = {
        ...testCase1,
        isProbation: true
    };

    const result4 = calculateMonthlySalary(testCase4);
    console.log('计算结果：');
    console.log('- 基本工资:', result4.adjustedBaseSalary);
    console.log('- 岗位工资:', result4.positionSalary);
    console.log('- 餐补:', result4.calculationResult.mealAllowance);
    console.log('- 通讯补贴:', result4.calculationResult.communicationAllowance);
    console.log('- 缺勤扣除:', result4.calculationResult.absenceDeduction);
    console.log('- 加班补助:', result4.calculationResult.attendanceAdjustment);
    console.log('- 应发工资:', result4.calculationResult.totalMonthlySalary);
    console.log('- 实发工资:', result4.calculationResult.netSalary);
    console.log('- 试用期状态:', result4.calculationResult.isProbation);
    console.log('- 试用期系数:', result4.calculationResult.probationFactor);
    
    // 验证试用期计算
    const normalTotal = result4.adjustedBaseSalary + result4.positionSalary + 
                       result4.calculationResult.mealAllowance + 
                       result4.calculationResult.communicationAllowance;
    const probationTotal = normalTotal * 0.8;
    console.log('- 正常总额:', normalTotal);
    console.log('- 试用期总额(80%):', probationTotal);
    console.log('- 计算总额:', result4.calculationResult.totalMonthlySalary);
    console.log('');

    console.log('=== 测试完成 ===');
}

// 运行测试
testSalaryCalculation();
