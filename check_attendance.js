const mongoose = require('mongoose');

// 连接数据库
mongoose.connect('mongodb://localhost:27017/mydb', {
    useNewUrlParser: true,
    useUnifiedTopology: true
});

// 定义薪资模型
const salarySchema = new mongoose.Schema({}, { strict: false });
const Salary = mongoose.model('Salary', salarySchema);

async function checkAttendance() {
    try {
        console.log('=== 检查实际出勤天数数据 ===\n');
        
        // 查询申杰的薪资记录
        const salaries = await Salary.find({ 
            employeeId: 'M023', 
            name: '申杰' 
        }).sort({ year: -1, month: -1 });
        
        console.log(`找到 ${salaries.length} 条薪资记录：\n`);
        
        salaries.forEach((salary, index) => {
            console.log(`记录 ${index + 1}:`);
            console.log(`- 年月: ${salary.year}年${salary.month}月`);
            console.log(`- 实际出勤天数: ${salary.actualAttendance} (类型: ${typeof salary.actualAttendance})`);
            console.log(`- 更新时间: ${salary.updatedAt}`);
            console.log(`- 创建时间: ${salary.createdAt}`);
            console.log('');
        });
        
        // 查询最新的记录
        const latestSalary = await Salary.findOne({ 
            employeeId: 'M023', 
            name: '申杰' 
        }).sort({ updatedAt: -1 });
        
        if (latestSalary) {
            console.log('=== 最新记录详情 ===');
            console.log(`年月: ${latestSalary.year}年${latestSalary.month}月`);
            console.log(`实际出勤天数: ${latestSalary.actualAttendance}`);
            console.log(`数据类型: ${typeof latestSalary.actualAttendance}`);
            console.log(`是否为数字: ${typeof latestSalary.actualAttendance === 'number'}`);
            console.log(`更新时间: ${latestSalary.updatedAt}`);
        }
        
    } catch (error) {
        console.error('查询失败:', error);
    } finally {
        mongoose.connection.close();
    }
}

checkAttendance();
