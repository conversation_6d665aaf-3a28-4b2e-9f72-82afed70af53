const express = require('express');
const router = express.Router();
const ProbationService = require('../services/ProbationService');
const authMiddleware = require('../middlewares/authMiddleware');

// 应用认证中间件
router.use(authMiddleware);

/**
 * 获取即将到期的试用期员工
 * GET /api/probation/upcoming
 */
router.get('/upcoming', async (req, res) => {
    try {
        const result = await ProbationService.getUpcomingProbationExpiry();
        res.json({
            success: true,
            data: result
        });
    } catch (error) {
        console.error('获取即将到期试用期员工失败:', error);
        res.status(500).json({
            success: false,
            message: '获取试用期到期信息失败',
            error: error.message
        });
    }
});

/**
 * 获取所有试用期员工
 * GET /api/probation/employees
 */
router.get('/employees', async (req, res) => {
    try {
        const employees = await ProbationService.getAllProbationEmployees();
        res.json({
            success: true,
            data: employees
        });
    } catch (error) {
        console.error('获取试用期员工失败:', error);
        res.status(500).json({
            success: false,
            message: '获取试用期员工信息失败',
            error: error.message
        });
    }
});

/**
 * 获取试用期统计信息
 * GET /api/probation/statistics
 */
router.get('/statistics', async (req, res) => {
    try {
        const statistics = await ProbationService.getProbationStatistics();
        res.json({
            success: true,
            data: statistics
        });
    } catch (error) {
        console.error('获取试用期统计信息失败:', error);
        res.status(500).json({
            success: false,
            message: '获取试用期统计信息失败',
            error: error.message
        });
    }
});

/**
 * 确认员工转正
 * POST /api/probation/confirm-regularization
 */
router.post('/confirm-regularization', async (req, res) => {
    try {
        const { employeeId, ...confirmationData } = req.body;

        if (!employeeId) {
            return res.status(400).json({
                success: false,
                message: '员工ID不能为空'
            });
        }

        const result = await ProbationService.confirmRegularization(employeeId, confirmationData);
        
        res.json({
            success: true,
            data: result,
            message: '员工转正确认成功'
        });
    } catch (error) {
        console.error('确认员工转正失败:', error);
        res.status(500).json({
            success: false,
            message: error.message || '确认员工转正失败'
        });
    }
});

/**
 * 延长试用期
 * POST /api/probation/extend
 */
router.post('/extend', async (req, res) => {
    try {
        const { employeeId, ...extensionData } = req.body;

        if (!employeeId) {
            return res.status(400).json({
                success: false,
                message: '员工ID不能为空'
            });
        }

        if (!extensionData.newEndDate) {
            return res.status(400).json({
                success: false,
                message: '新的试用期结束日期不能为空'
            });
        }

        const result = await ProbationService.extendProbation(employeeId, extensionData);
        
        res.json({
            success: true,
            data: result,
            message: '试用期延长成功'
        });
    } catch (error) {
        console.error('延长试用期失败:', error);
        res.status(500).json({
            success: false,
            message: error.message || '延长试用期失败'
        });
    }
});

/**
 * 批量处理试用期员工
 * POST /api/probation/batch-process
 */
router.post('/batch-process', async (req, res) => {
    try {
        const { employeeIds, action, actionData } = req.body;

        if (!employeeIds || !Array.isArray(employeeIds) || employeeIds.length === 0) {
            return res.status(400).json({
                success: false,
                message: '员工ID列表不能为空'
            });
        }

        if (!action || !['regularization', 'extension'].includes(action)) {
            return res.status(400).json({
                success: false,
                message: '操作类型无效'
            });
        }

        const results = [];
        const errors = [];

        for (const employeeId of employeeIds) {
            try {
                let result;
                if (action === 'regularization') {
                    result = await ProbationService.confirmRegularization(employeeId, actionData);
                } else if (action === 'extension') {
                    result = await ProbationService.extendProbation(employeeId, actionData);
                }
                results.push({ employeeId, success: true, result });
            } catch (error) {
                errors.push({ employeeId, success: false, error: error.message });
            }
        }

        res.json({
            success: true,
            data: {
                processed: results.length,
                errors: errors.length,
                results,
                errors
            },
            message: `批量处理完成，成功 ${results.length} 个，失败 ${errors.length} 个`
        });
    } catch (error) {
        console.error('批量处理试用期员工失败:', error);
        res.status(500).json({
            success: false,
            message: '批量处理失败',
            error: error.message
        });
    }
});

module.exports = router;
