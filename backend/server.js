const express = require('express');
const cors = require('cors');
const mongoose = require('mongoose');
const path = require('path');
const authRoutes = require('./routes/auth');
const employeeRoutes = require('./routes/employees');
const salaryRoutes = require('./routes/salaryRoutes');
const healthRoutes = require('./routes/healthRoutes');
const adminRoutes = require('./routes/adminRoutes');
const { rootHealthCheck } = require('./routes/healthRoutes');
const connectDB = require('./config/db');
const createInitialUsers = require('./utils/createInitialUsers');
const authMiddleware = require('./authMiddleware');
const adminMiddleware = require('./middlewares/adminMiddleware');
const attendanceRoutes = require('./routes/attendance');
const attendanceConfigRoutes = require('./routes/attendanceConfig');
const holidayRoutes = require('./routes/holidayRoutes');
const WorkDayService = require('./services/WorkDayService');
const probationRoutes = require('./routes/probationRoutes');

const app = express();

// 中间件配置
app.use(cors());
app.use(express.json());

// 添加根路径的健康检查
app.get('/health', rootHealthCheck);

// API路由（确保在静态文件服务之前）
app.use('/api/auth', authRoutes);
app.use('/api/employees', employeeRoutes);
app.use('/api/salary', salaryRoutes);
// 临时移除认证要求，方便调试
app.use('/api/admin', adminRoutes);
app.use('/api', healthRoutes);
app.use('/api/attendance', attendanceRoutes);
app.use('/api/attendance-config', attendanceConfigRoutes);
app.use('/api/holidays', holidayRoutes);
app.use('/api/probation', probationRoutes);

// 根据环境配置静态文件路径
const staticPath = process.env.NODE_ENV === 'production'
    ? path.join(__dirname, 'public')
    : path.join(__dirname, '../frontend/build');

// 确保API路由在静态文件服务之前定义
app.use('/api/health', (_req, res) => {
    res.json({
        status: 'healthy',
        timestamp: new Date(),
        environment: process.env.NODE_ENV,
        mongodb_uri: process.env.MONGODB_URI ? process.env.MONGODB_URI.split('@').pop() : 'not_set'
    });
});

app.use(express.static(staticPath));
// 通配路由处理（放在最后）
app.get('*', (_req, res) => {
    const indexPath = process.env.NODE_ENV === 'production'
        ? path.join(__dirname, 'public/index.html')
        : path.join(__dirname, '../frontend/build/index.html');
    res.sendFile(indexPath);
});

// 连接数据库
connectDB()
.then(async () => {
    // 创建初始用户
    await createInitialUsers();
    
    // 自动检查节假日配置
    console.log('🔍 执行节假日配置自动检查...');
    try {
        await WorkDayService.systemStartupCheck();
        console.log('✅ 节假日配置检查完成');
    } catch (error) {
        console.warn('⚠️ 节假日配置检查失败:', error.message);
    }

    // 数据库连接成功后启动服务器
    const port = process.env.PORT || 5006;
    const host = process.env.HOST || '0.0.0.0';

    // 确保监听所有网络接口，而不仅仅是localhost
    app.listen(port, host, () => {
        console.log(`服务器启动成功，监听端口 ${port}，地址: ${host}`);
        console.log(`运行环境: ${process.env.NODE_ENV || 'development'}`);
        console.log(`健康检查地址: http://${host === '0.0.0.0' ? 'localhost' : host}:${port}/health`);
        console.log(`API地址: http://${host === '0.0.0.0' ? 'localhost' : host}:${port}/api`);
    });
})
.catch(err => {
    console.error('无法启动服务器:', err);
    process.exit(1);
});
