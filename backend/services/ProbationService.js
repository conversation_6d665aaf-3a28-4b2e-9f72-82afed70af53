const Employee = require('../models/Employee');

class ProbationService {
    /**
     * 获取即将到期的试用期员工（提前一周提醒）
     */
    static async getUpcomingProbationExpiry() {
        try {
            const today = new Date();
            const oneWeekLater = new Date();
            oneWeekLater.setDate(today.getDate() + 7);

            // 查找试用期员工
            const probationEmployees = await Employee.find({
                workType: '试用',
                status: '在职',
                probationEndDate: { $exists: true, $ne: '' }
            });

            const upcomingExpiry = [];
            const expired = [];

            for (const employee of probationEmployees) {
                if (!employee.probationEndDate) continue;

                const endDate = new Date(employee.probationEndDate);
                const diffTime = endDate.getTime() - today.getTime();
                const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

                if (diffDays < 0) {
                    // 已过期
                    expired.push({
                        ...employee.toObject(),
                        daysOverdue: Math.abs(diffDays),
                        status: 'expired'
                    });
                } else if (diffDays <= 7) {
                    // 即将到期（一周内）
                    upcomingExpiry.push({
                        ...employee.toObject(),
                        daysRemaining: diffDays,
                        status: 'upcoming'
                    });
                }
            }

            return {
                upcoming: upcomingExpiry,
                expired: expired,
                total: upcomingExpiry.length + expired.length
            };
        } catch (error) {
            console.error('获取试用期到期信息失败:', error);
            throw error;
        }
    }

    /**
     * 获取所有试用期员工的详细信息
     */
    static async getAllProbationEmployees() {
        try {
            const today = new Date();
            
            const probationEmployees = await Employee.find({
                workType: '试用',
                status: '在职',
                probationEndDate: { $exists: true, $ne: '' }
            }).sort({ probationEndDate: 1 });

            return probationEmployees.map(employee => {
                const endDate = new Date(employee.probationEndDate);
                const diffTime = endDate.getTime() - today.getTime();
                const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

                let probationStatus = 'normal';
                let statusText = '正常';
                let daysInfo = '';

                if (diffDays < 0) {
                    probationStatus = 'expired';
                    statusText = '已过期';
                    daysInfo = `已过期 ${Math.abs(diffDays)} 天`;
                } else if (diffDays <= 7) {
                    probationStatus = 'upcoming';
                    statusText = '即将到期';
                    daysInfo = `还有 ${diffDays} 天到期`;
                } else {
                    daysInfo = `还有 ${diffDays} 天到期`;
                }

                return {
                    ...employee.toObject(),
                    probationStatus,
                    statusText,
                    daysInfo,
                    daysRemaining: diffDays > 0 ? diffDays : 0,
                    daysOverdue: diffDays < 0 ? Math.abs(diffDays) : 0
                };
            });
        } catch (error) {
            console.error('获取试用期员工信息失败:', error);
            throw error;
        }
    }

    /**
     * 确认员工转正
     */
    static async confirmRegularization(employeeId, confirmationData) {
        try {
            const { 
                regularizationDate, 
                newPosition, 
                newDepartment, 
                newSubDepartment,
                remarks 
            } = confirmationData;

            // 查找员工
            const employee = await Employee.findOne({ employeeId });
            if (!employee) {
                throw new Error('员工不存在');
            }

            if (employee.workType !== '试用') {
                throw new Error('该员工不是试用期员工');
            }

            // 更新员工信息
            const updateData = {
                workType: '全职',
                probationMonths: '无',
                probationEndDate: '',
                regularizationDate: regularizationDate || new Date().toISOString().split('T')[0]
            };

            // 如果提供了新的岗位信息，则更新
            if (newPosition) updateData.position = newPosition;
            if (newDepartment) updateData.department = newDepartment;
            if (newSubDepartment !== undefined) updateData.subDepartment = newSubDepartment;

            const updatedEmployee = await Employee.findOneAndUpdate(
                { employeeId },
                updateData,
                { new: true }
            );

            // 记录转正日志
            console.log(`员工 ${employee.name} (${employeeId}) 已确认转正`, {
                regularizationDate: updateData.regularizationDate,
                newPosition,
                newDepartment,
                newSubDepartment,
                remarks
            });

            return {
                success: true,
                employee: updatedEmployee,
                message: '员工转正确认成功'
            };
        } catch (error) {
            console.error('确认员工转正失败:', error);
            throw error;
        }
    }

    /**
     * 延长试用期
     */
    static async extendProbation(employeeId, extensionData) {
        try {
            const { newEndDate, extensionReason, extensionMonths } = extensionData;

            const employee = await Employee.findOne({ employeeId });
            if (!employee) {
                throw new Error('员工不存在');
            }

            if (employee.workType !== '试用') {
                throw new Error('该员工不是试用期员工');
            }

            // 更新试用期结束日期
            const updatedEmployee = await Employee.findOneAndUpdate(
                { employeeId },
                { 
                    probationEndDate: newEndDate,
                    probationExtensionReason: extensionReason,
                    probationExtensionMonths: extensionMonths
                },
                { new: true }
            );

            console.log(`员工 ${employee.name} (${employeeId}) 试用期已延长`, {
                newEndDate,
                extensionReason,
                extensionMonths
            });

            return {
                success: true,
                employee: updatedEmployee,
                message: '试用期延长成功'
            };
        } catch (error) {
            console.error('延长试用期失败:', error);
            throw error;
        }
    }

    /**
     * 计算试用期统计信息
     */
    static async getProbationStatistics() {
        try {
            const today = new Date();
            const oneWeekLater = new Date();
            oneWeekLater.setDate(today.getDate() + 7);

            const allProbationEmployees = await Employee.find({
                workType: '试用',
                status: '在职'
            });

            let total = 0;
            let upcoming = 0;
            let expired = 0;
            let normal = 0;

            for (const employee of allProbationEmployees) {
                if (!employee.probationEndDate) continue;
                
                total++;
                const endDate = new Date(employee.probationEndDate);
                const diffTime = endDate.getTime() - today.getTime();
                const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

                if (diffDays < 0) {
                    expired++;
                } else if (diffDays <= 7) {
                    upcoming++;
                } else {
                    normal++;
                }
            }

            return {
                total,
                upcoming,
                expired,
                normal
            };
        } catch (error) {
            console.error('获取试用期统计信息失败:', error);
            throw error;
        }
    }
}

module.exports = ProbationService;
