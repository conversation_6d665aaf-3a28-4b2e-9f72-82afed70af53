const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');

// 连接数据库
mongoose.connect('mongodb://localhost:27017/mydb', {
    useNewUrlParser: true,
    useUnifiedTopology: true
});

// 定义用户模型
const userSchema = new mongoose.Schema({
    username: {
        type: String,
        required: true,
        unique: true,
        minlength: 3,
        maxlength: 20,
        match: /^[a-zA-Z0-9_]+$/
    },
    password: {
        type: String,
        required: true,
        minlength: 6
    },
    phone: {
        type: String,
        validate: {
            validator: function(v) {
                return /^1[3-9]\d{9}$/.test(v);
            },
            message: props => `${props.value} 不是有效的手机号码!`
        },
        unique: true,
        sparse: true
    },
    resetPasswordToken: String,
    resetPasswordExpires: Date,
    role: {
        type: String,
        enum: ['admin', 'user'],
        default: 'user'
    }
}, {
    timestamps: true
});

// 密码哈希中间件
userSchema.pre('save', async function(next) {
    if (this.isModified('password')) {
        this.password = await bcrypt.hash(this.password, 10);
    }
    next();
});

// 添加密码验证方法
userSchema.methods.comparePassword = async function(candidatePassword) {
    return bcrypt.compare(candidatePassword, this.password);
};

const User = mongoose.model('User', userSchema);

async function checkAdminUser() {
    try {
        console.log('=== 检查admin用户状态 ===\n');
        
        // 查找admin用户
        const adminUser = await User.findOne({ username: 'admin' });
        
        if (!adminUser) {
            console.log('❌ 未找到admin用户');
            return;
        }
        
        console.log('✅ 找到admin用户:');
        console.log(`- 用户ID: ${adminUser._id}`);
        console.log(`- 用户名: ${adminUser.username}`);
        console.log(`- 角色: ${adminUser.role}`);
        console.log(`- 密码哈希: ${adminUser.password.substring(0, 20)}...`);
        console.log(`- 创建时间: ${adminUser.createdAt}`);
        console.log(`- 更新时间: ${adminUser.updatedAt}`);
        console.log('');
        
        // 测试常见密码
        const testPasswords = ['admin123', 'admin', '123456', 'password'];
        
        console.log('=== 测试密码 ===');
        for (const testPassword of testPasswords) {
            try {
                const isMatch = await adminUser.comparePassword(testPassword);
                console.log(`密码 "${testPassword}": ${isMatch ? '✅ 匹配' : '❌ 不匹配'}`);
                
                if (isMatch) {
                    console.log(`\n🎉 找到正确密码: ${testPassword}`);
                    break;
                }
            } catch (error) {
                console.log(`密码 "${testPassword}": ❌ 验证出错 - ${error.message}`);
            }
        }
        
        // 检查密码哈希是否有效
        console.log('\n=== 密码哈希分析 ===');
        console.log(`密码哈希长度: ${adminUser.password.length}`);
        console.log(`是否以$2开头: ${adminUser.password.startsWith('$2')}`);
        
        // 手动测试bcrypt
        console.log('\n=== 手动测试bcrypt ===');
        try {
            const testResult = await bcrypt.compare('admin123', adminUser.password);
            console.log(`手动bcrypt.compare('admin123', hash): ${testResult}`);
        } catch (error) {
            console.log(`手动bcrypt测试出错: ${error.message}`);
        }
        
    } catch (error) {
        console.error('检查失败:', error);
    } finally {
        mongoose.connection.close();
    }
}

checkAdminUser();
