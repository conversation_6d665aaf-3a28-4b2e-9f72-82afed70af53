const mongoose = require('mongoose');
const Employee = require('./backend/models/Employee');

async function createTestProbationEmployees() {
    try {
        await mongoose.connect('mongodb://localhost:27017/mydb');
        console.log('数据库连接成功');

        // 创建测试试用期员工
        const today = new Date();
        const oneWeekLater = new Date();
        oneWeekLater.setDate(today.getDate() + 5); // 5天后到期

        const twoWeeksLater = new Date();
        twoWeeksLater.setDate(today.getDate() + 15); // 15天后到期

        const expired = new Date();
        expired.setDate(today.getDate() - 3); // 3天前已过期

        const testEmployees = [
            {
                name: '张试用',
                employeeId: 'T001',
                workType: '试用',
                status: '在职',
                entryDate: new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // 30天前入职
                probationMonths: '3',
                probationEndDate: oneWeekLater.toISOString().split('T')[0], // 5天后到期
                department: '技术部',
                position: '软件工程师',
                phone: '13800000001',
                idNumber: '110101199001010001'
            },
            {
                name: '李试用',
                employeeId: 'T002',
                workType: '试用',
                status: '在职',
                entryDate: new Date(today.getTime() - 60 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // 60天前入职
                probationMonths: '3',
                probationEndDate: twoWeeksLater.toISOString().split('T')[0], // 15天后到期
                department: '市场部',
                position: '市场专员',
                phone: '13800000002',
                idNumber: '110101199001010002'
            },
            {
                name: '王过期',
                employeeId: 'T003',
                workType: '试用',
                status: '在职',
                entryDate: new Date(today.getTime() - 90 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // 90天前入职
                probationMonths: '3',
                probationEndDate: expired.toISOString().split('T')[0], // 3天前已过期
                department: '人事部',
                position: '人事专员',
                phone: '13800000003',
                idNumber: '110101199001010003'
            }
        ];

        // 删除已存在的测试员工
        await Employee.deleteMany({ employeeId: { $in: ['T001', 'T002', 'T003'] } });
        console.log('清理已存在的测试数据');

        // 创建新的测试员工
        for (const empData of testEmployees) {
            const employee = new Employee(empData);
            await employee.save();
            console.log(`创建测试员工: ${empData.name} (${empData.employeeId}), 试用期截止: ${empData.probationEndDate}`);
        }

        console.log('\n测试数据创建完成！');
        console.log('现在可以测试试用期管理功能了。');

        process.exit(0);
    } catch (error) {
        console.error('创建测试数据失败:', error);
        process.exit(1);
    }
}

createTestProbationEmployees();
