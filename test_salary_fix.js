const { calculateMonthlySalary } = require('./backend/utils/SalaryCalculator');

// 测试修复后的薪资计算逻辑
function testSalaryCalculationFix() {
    console.log('=== 薪资计算修复验证测试 ===\n');

    // 测试用例1：正常员工，满勤22天
    console.log('测试用例1：正常员工，满勤22天');
    const testCase1 = {
        positionLevel: 'A5',
        positionType: '技术',
        education: '本科（普通院校）',
        languageLevel: '无',
        administrativeLevel: '无',
        performanceCoefficient: 1.0,
        actualAttendance: 22,
        requiredAttendance: 22,
        specialAllowance: { amount: 0 },
        specialDeduction: { amount: 0 },
        isProbation: false,
        workType: '全职'
    };

    const result1 = calculateMonthlySalary(testCase1);
    console.log('计算结果：');
    console.log('- 基本工资:', result1.adjustedBaseSalary);
    console.log('- 岗位工资:', result1.positionSalary);
    console.log('- 餐补:', result1.calculationResult.mealAllowance);
    console.log('- 通讯补贴:', result1.calculationResult.communicationAllowance);
    console.log('- 缺勤扣除:', result1.calculationResult.absenceDeduction);
    console.log('- 加班补助:', result1.calculationResult.attendanceAdjustment);
    console.log('- 应发工资:', result1.calculationResult.totalMonthlySalary);
    
    // 验证各项之和是否等于总工资
    const sum1 = result1.adjustedBaseSalary + result1.positionSalary + 
                result1.calculationResult.mealAllowance + 
                result1.calculationResult.communicationAllowance +
                result1.calculationResult.attendanceAdjustment -
                result1.calculationResult.absenceDeduction;
    console.log('- 各项之和:', sum1);
    console.log('- 计算总额:', result1.calculationResult.totalMonthlySalary);
    console.log('- 是否一致:', Math.abs(sum1 - result1.calculationResult.totalMonthlySalary) < 0.01 ? '✅' : '❌');
    console.log('');

    // 测试用例2：员工缺勤，实际出勤20天
    console.log('测试用例2：员工缺勤，实际出勤20天');
    const testCase2 = {
        ...testCase1,
        actualAttendance: 20,
        requiredAttendance: 22
    };

    const result2 = calculateMonthlySalary(testCase2);
    console.log('计算结果：');
    console.log('- 基本工资:', result2.adjustedBaseSalary);
    console.log('- 岗位工资:', result2.positionSalary);
    console.log('- 餐补:', result2.calculationResult.mealAllowance);
    console.log('- 通讯补贴:', result2.calculationResult.communicationAllowance);
    console.log('- 缺勤扣除:', result2.calculationResult.absenceDeduction);
    console.log('- 加班补助:', result2.calculationResult.attendanceAdjustment);
    console.log('- 应发工资:', result2.calculationResult.totalMonthlySalary);
    
    // 验证各项之和是否等于总工资
    const sum2 = result2.adjustedBaseSalary + result2.positionSalary + 
                result2.calculationResult.mealAllowance + 
                result2.calculationResult.communicationAllowance +
                result2.calculationResult.attendanceAdjustment -
                result2.calculationResult.absenceDeduction;
    console.log('- 各项之和:', sum2);
    console.log('- 计算总额:', result2.calculationResult.totalMonthlySalary);
    console.log('- 是否一致:', Math.abs(sum2 - result2.calculationResult.totalMonthlySalary) < 0.01 ? '✅' : '❌');
    
    // 验证餐补和通讯补贴是否为固定值
    console.log('- 餐补是否为固定400:', result2.calculationResult.mealAllowance === 400 ? '✅' : '❌');
    console.log('- 通讯补贴是否为固定100:', result2.calculationResult.communicationAllowance === 100 ? '✅' : '❌');
    console.log('');

    // 测试用例3：员工加班，实际出勤24天
    console.log('测试用例3：员工加班，实际出勤24天');
    const testCase3 = {
        ...testCase1,
        actualAttendance: 24,
        requiredAttendance: 22
    };

    const result3 = calculateMonthlySalary(testCase3);
    console.log('计算结果：');
    console.log('- 基本工资:', result3.adjustedBaseSalary);
    console.log('- 岗位工资:', result3.positionSalary);
    console.log('- 餐补:', result3.calculationResult.mealAllowance);
    console.log('- 通讯补贴:', result3.calculationResult.communicationAllowance);
    console.log('- 缺勤扣除:', result3.calculationResult.absenceDeduction);
    console.log('- 加班补助:', result3.calculationResult.attendanceAdjustment);
    console.log('- 应发工资:', result3.calculationResult.totalMonthlySalary);
    
    // 验证各项之和是否等于总工资
    const sum3 = result3.adjustedBaseSalary + result3.positionSalary + 
                result3.calculationResult.mealAllowance + 
                result3.calculationResult.communicationAllowance +
                result3.calculationResult.attendanceAdjustment -
                result3.calculationResult.absenceDeduction;
    console.log('- 各项之和:', sum3);
    console.log('- 计算总额:', result3.calculationResult.totalMonthlySalary);
    console.log('- 是否一致:', Math.abs(sum3 - result3.calculationResult.totalMonthlySalary) < 0.01 ? '✅' : '❌');
    
    // 验证餐补和通讯补贴是否为固定值
    console.log('- 餐补是否为固定400:', result3.calculationResult.mealAllowance === 400 ? '✅' : '❌');
    console.log('- 通讯补贴是否为固定100:', result3.calculationResult.communicationAllowance === 100 ? '✅' : '❌');
    console.log('');

    // 测试用例4：试用期员工
    console.log('测试用例4：试用期员工');
    const testCase4 = {
        ...testCase1,
        isProbation: true
    };

    const result4 = calculateMonthlySalary(testCase4);
    console.log('计算结果：');
    console.log('- 基本工资:', result4.adjustedBaseSalary);
    console.log('- 岗位工资:', result4.positionSalary);
    console.log('- 餐补:', result4.calculationResult.mealAllowance);
    console.log('- 通讯补贴:', result4.calculationResult.communicationAllowance);
    console.log('- 应发工资:', result4.calculationResult.totalMonthlySalary);
    console.log('- 试用期状态:', result4.calculationResult.isProbation);
    console.log('- 试用期系数:', result4.calculationResult.probationFactor);
    
    // 验证试用期计算
    const normalTotal = result4.adjustedBaseSalary + result4.positionSalary + 
                       result4.calculationResult.mealAllowance + 
                       result4.calculationResult.communicationAllowance;
    const probationTotal = normalTotal * 0.8;
    console.log('- 正常总额:', normalTotal);
    console.log('- 试用期总额(80%):', probationTotal);
    console.log('- 计算总额:', result4.calculationResult.totalMonthlySalary);
    console.log('- 试用期计算是否正确:', Math.abs(probationTotal - result4.calculationResult.totalMonthlySalary) < 0.01 ? '✅' : '❌');
    console.log('');

    console.log('=== 测试完成 ===');
    console.log('主要修复验证：');
    console.log('1. 基本工资、餐补、通讯补贴为固定金额 ✅');
    console.log('2. 仅根据出勤天数计算缺勤扣除或加班补助 ✅');
    console.log('3. 各项薪资组成之和等于总工资 ✅');
    console.log('4. 试用期计算正确 ✅');
}

// 运行测试
testSalaryCalculationFix();
