// 测试前端逻辑
function testActualAttendanceLogic() {
    console.log('=== 测试前端 actualAttendance 逻辑 ===\n');
    
    // 模拟不同的 salaryInfo 数据
    const testCases = [
        {
            name: '测试1: actualAttendance = 21',
            salaryInfo: { actualAttendance: 21 },
            expected: 21
        },
        {
            name: '测试2: actualAttendance = 0',
            salaryInfo: { actualAttendance: 0 },
            expected: 0
        },
        {
            name: '测试3: actualAttendance = undefined',
            salaryInfo: { actualAttendance: undefined },
            expected: 0
        },
        {
            name: '测试4: actualAttendance = null',
            salaryInfo: { actualAttendance: null },
            expected: 0
        },
        {
            name: '测试5: actualAttendance = 22',
            salaryInfo: { actualAttendance: 22 },
            expected: 22
        }
    ];
    
    testCases.forEach(testCase => {
        const { salaryInfo } = testCase;
        
        // 模拟修复后的逻辑
        let result;
        if (salaryInfo.actualAttendance !== undefined && salaryInfo.actualAttendance !== null) {
            result = salaryInfo.actualAttendance;
        } else {
            result = 0;
        }
        
        console.log(`${testCase.name}:`);
        console.log(`  输入: ${salaryInfo.actualAttendance}`);
        console.log(`  期望: ${testCase.expected}`);
        console.log(`  实际: ${result}`);
        console.log(`  结果: ${result === testCase.expected ? '✅ 通过' : '❌ 失败'}`);
        console.log('');
    });
    
    // 测试旧逻辑（有问题的）
    console.log('=== 对比旧逻辑（有问题的）===\n');
    
    testCases.forEach(testCase => {
        const { salaryInfo } = testCase;
        
        // 模拟旧的有问题的逻辑
        let oldResult;
        if (salaryInfo.actualAttendance && 
            salaryInfo.actualAttendance !== 22) {
            oldResult = salaryInfo.actualAttendance;
        } else {
            oldResult = 0;
        }
        
        console.log(`${testCase.name} (旧逻辑):`);
        console.log(`  输入: ${salaryInfo.actualAttendance}`);
        console.log(`  旧逻辑结果: ${oldResult}`);
        console.log(`  是否正确: ${oldResult === testCase.expected ? '✅' : '❌'}`);
        console.log('');
    });
}

testActualAttendanceLogic();
