{"name": "mchrms", "version": "1.3.1", "description": "员工管理系统", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "node start.js", "check": "node checkDependencies.js", "install-all": "npm install && cd frontend && npm install && cd ../backend && npm install"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@ant-design/icons": "^6.0.0", "@antv/g2plot": "^2.4.31", "antd": "^4.24.13", "axios": "^1.8.4", "bcryptjs": "^2.4.3", "child_process": "^1.0.2", "cors": "^2.8.5", "express": "^4.21.2", "jsonwebtoken": "^9.0.2", "mongodb": "^5.9.2", "mongoose": "^8.13.1", "node-fetch": "^2.7.0", "pdfmake": "^0.2.18", "pinyin-pro": "^3.26.0", "react-datepicker": "^8.3.0", "react-draggable": "^4.4.6", "redis": "^4.6.7", "util": "^0.12.5"}, "devDependencies": {"@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.2.0", "@types/bcryptjs": "^2.4.6", "@types/node": "^22.14.0", "concurrently": "^9.1.2", "electron-builder": "^26.0.12", "electron-is-dev": "^3.0.1", "jest": "^29.7.0", "typescript": "^5.8.3", "wait-on": "^8.0.3"}}