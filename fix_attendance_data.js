const mongoose = require('mongoose');

// 连接数据库
mongoose.connect('mongodb://localhost:27017/mydb', {
    useNewUrlParser: true,
    useUnifiedTopology: true
});

// 定义薪资模型
const salarySchema = new mongoose.Schema({}, { strict: false });
const Salary = mongoose.model('Salary', salarySchema);

// 定义员工模型
const employeeSchema = new mongoose.Schema({}, { strict: false });
const Employee = mongoose.model('Employee', employeeSchema);

async function fixAttendanceData() {
    try {
        console.log('=== 修复实际出勤天数数据 ===\n');
        
        // 获取所有员工信息
        const employees = await Employee.find({});
        console.log(`找到 ${employees.length} 个员工\n`);
        
        let fixedCount = 0;
        
        for (const employee of employees) {
            // 获取员工的岗位类型
            const positionType = employee.positionType || '其他';
            
            console.log(`处理员工: ${employee.name} (${employee.employeeId}) - 岗位类型: ${positionType}`);
            
            // 查找该员工的所有薪资记录
            const salaryRecords = await Salary.find({ 
                employeeId: employee.employeeId 
            });
            
            console.log(`  找到 ${salaryRecords.length} 条薪资记录`);
            
            for (const record of salaryRecords) {
                let shouldUpdate = false;
                let newActualAttendance = record.actualAttendance;
                
                // 如果是高管，保持当前值（可能是正确的）
                if (positionType === '高管') {
                    console.log(`  ${record.year}年${record.month}月: 高管，保持当前值 ${record.actualAttendance}`);
                    continue;
                }
                
                // 如果是非高管且实际出勤天数为22，需要重置为0
                if (record.actualAttendance === 22) {
                    newActualAttendance = 0;
                    shouldUpdate = true;
                    console.log(`  ${record.year}年${record.month}月: 非高管，从22重置为0`);
                } else {
                    console.log(`  ${record.year}年${record.month}月: 保持当前值 ${record.actualAttendance}`);
                }
                
                if (shouldUpdate) {
                    await Salary.updateOne(
                        { _id: record._id },
                        { 
                            $set: { 
                                actualAttendance: newActualAttendance,
                                updatedAt: new Date()
                            }
                        }
                    );
                    fixedCount++;
                    console.log(`    ✅ 已更新`);
                }
            }
            
            console.log('');
        }
        
        console.log(`=== 修复完成 ===`);
        console.log(`总共修复了 ${fixedCount} 条记录`);
        
        // 验证修复结果
        console.log('\n=== 验证修复结果 ===');
        const nonExecutiveWith22 = await Salary.aggregate([
            {
                $lookup: {
                    from: 'employees',
                    localField: 'employeeId',
                    foreignField: 'employeeId',
                    as: 'employee'
                }
            },
            {
                $match: {
                    actualAttendance: 22,
                    'employee.positionType': { $ne: '高管' }
                }
            }
        ]);
        
        console.log(`还有 ${nonExecutiveWith22.length} 条非高管记录的实际出勤天数为22天`);
        
        if (nonExecutiveWith22.length > 0) {
            console.log('这些记录可能需要手动检查：');
            nonExecutiveWith22.forEach(record => {
                console.log(`- ${record.name} (${record.employeeId}) ${record.year}年${record.month}月`);
            });
        }
        
    } catch (error) {
        console.error('修复失败:', error);
    } finally {
        mongoose.connection.close();
    }
}

fixAttendanceData();
